#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试添加技能时的编码问题
"""

import os
import sys
import numpy as np
import cv2
from skill_processor import HekiliProcessor

def test_add_skill_to_chinese_config():
    """测试向中文配置添加技能"""
    print("=== 测试向中文配置添加技能 ===")
    
    try:
        processor = HekiliProcessor()
        
        # 使用中文配置
        config_name = "鸟德一号"
        print(f"测试配置: {config_name}")
        
        # 加载配置
        if not processor.load_config(config_name):
            print("❌ 配置加载失败")
            return False
        
        print(f"✅ 配置加载成功，当前技能数量: {len(processor.icon_bindings)}")
        
        # 创建测试模板
        test_template = np.random.randint(0, 255, (48, 48, 3), dtype=np.uint8)
        
        # 添加技能
        skill_name = "S-encoding-test"
        hotkey = "e"
        text = "编码测试技能"
        
        print(f"添加技能: {skill_name} -> {hotkey}")
        
        # 添加技能绑定
        binding = processor.add_icon_binding(
            name=skill_name,
            text=text,
            hotkey=hotkey,
            template_image=test_template
        )
        
        if binding:
            print(f"✅ 技能绑定创建成功: {text}")
            print(f"   绑定信息: name={binding.name}, hotkey={binding.hotkey}")
            
            # 保存配置
            print("保存配置...")
            save_result = processor.save_config(config_name)
            
            if save_result:
                print("✅ 配置保存成功")
                
                # 检查模板文件
                expected_template_path = f"templates/{config_name}_{skill_name}.png"
                print(f"检查模板文件: {expected_template_path}")
                
                if os.path.exists(expected_template_path):
                    print("✅ 模板文件创建成功")
                    
                    # 检查文件名编码
                    try:
                        # 尝试用不同编码读取文件名
                        filename = os.path.basename(expected_template_path)
                        print(f"文件名: {filename}")
                        print(f"文件名编码测试:")
                        print(f"  UTF-8: {filename.encode('utf-8')}")
                        print(f"  GBK: {filename.encode('gbk', errors='ignore')}")
                        
                        # 检查文件大小
                        file_size = os.path.getsize(expected_template_path)
                        print(f"文件大小: {file_size} bytes")
                        
                        if file_size > 0:
                            print("✅ 模板文件内容正常")
                        else:
                            print("❌ 模板文件为空")
                            return False
                            
                    except Exception as e:
                        print(f"❌ 文件名编码检查失败: {e}")
                        return False
                        
                else:
                    print("❌ 模板文件未创建")
                    
                    # 列出templates目录中的相关文件
                    templates_dir = "templates"
                    if os.path.exists(templates_dir):
                        print("templates目录中的相关文件:")
                        for file in os.listdir(templates_dir):
                            if config_name in file or skill_name in file:
                                print(f"  - {file}")
                    
                    return False
                    
            else:
                print("❌ 配置保存失败")
                return False
                
        else:
            print("❌ 技能绑定创建失败")
            return False
        
        # 测试重新加载
        print("\n测试重新加载配置...")
        processor.clear_bindings()
        
        if processor.load_config(config_name):
            print(f"✅ 重新加载成功，技能数量: {len(processor.icon_bindings)}")
            
            if skill_name in processor.icon_bindings:
                print(f"✅ 新添加的技能存在: {skill_name}")
                return True
            else:
                print(f"❌ 新添加的技能丢失: {skill_name}")
                print("现有技能:")
                for name in processor.icon_bindings.keys():
                    print(f"  - {name}")
                return False
        else:
            print("❌ 重新加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_template_path_generation():
    """测试模板路径生成"""
    print("\n=== 测试模板路径生成 ===")
    
    try:
        # 测试不同的配置名称和技能名称组合
        test_cases = [
            ("鸟德一号", "S-1"),
            ("测试中文配置", "S-test"),
            ("BFS1", "S-1"),
            ("很长的中文配置名称", "S-long")
        ]
        
        for config_name, skill_name in test_cases:
            # 模拟skill_processor.py中的路径生成逻辑
            template_path = os.path.join("templates", f"{config_name}_{skill_name}.png")
            
            print(f"配置: {config_name}, 技能: {skill_name}")
            print(f"  生成路径: {template_path}")
            
            # 检查路径编码
            try:
                path_bytes = template_path.encode('utf-8')
                print(f"  UTF-8编码: {path_bytes}")
                
                # 检查是否可以创建文件
                test_content = b"test"
                with open(template_path, 'wb') as f:
                    f.write(test_content)
                
                if os.path.exists(template_path):
                    print(f"  ✅ 文件创建成功")
                    os.remove(template_path)  # 清理测试文件
                else:
                    print(f"  ❌ 文件创建失败")
                    
            except Exception as e:
                print(f"  ❌ 路径编码错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 路径生成测试失败: {e}")
        return False

def check_system_encoding():
    """检查系统编码设置"""
    print("\n=== 系统编码检查 ===")
    
    try:
        print(f"系统默认编码: {sys.getdefaultencoding()}")
        print(f"文件系统编码: {sys.getfilesystemencoding()}")
        print(f"标准输出编码: {sys.stdout.encoding}")
        
        # 测试中文字符处理
        chinese_text = "鸟德一号"
        print(f"中文文本: {chinese_text}")
        print(f"UTF-8编码: {chinese_text.encode('utf-8')}")
        
        # 测试文件名处理
        test_filename = f"{chinese_text}_test.txt"
        print(f"测试文件名: {test_filename}")
        
        # 尝试创建和删除文件
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write("测试内容")
        
        if os.path.exists(test_filename):
            print("✅ 中文文件名创建成功")
            os.remove(test_filename)
            print("✅ 中文文件名删除成功")
        else:
            print("❌ 中文文件名创建失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 系统编码检查失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    try:
        # 清理测试模板文件
        test_files = [
            "templates/鸟德一号_S-encoding-test.png",
            "templates/测试中文配置_S-test.png",
            "templates/很长的中文配置名称_S-long.png"
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ 已删除: {file_path}")
        
        # 从配置中移除测试技能
        from skill_processor import HekiliProcessor
        processor = HekiliProcessor()
        
        if processor.load_config("鸟德一号"):
            if "S-encoding-test" in processor.icon_bindings:
                del processor.icon_bindings["S-encoding-test"]
                processor.save_config("鸟德一号")
                print("✅ 已从配置中移除测试技能")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理测试文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("添加技能编码问题测试")
    print("=" * 50)
    
    # 运行测试
    test1 = check_system_encoding()
    test2 = test_template_path_generation()
    test3 = test_add_skill_to_chinese_config()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"系统编码检查: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"模板路径生成: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"添加技能到中文配置: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if test1 and test2 and test3:
        print("\n🎉 所有测试通过！添加技能编码正常")
    else:
        print("\n⚠️  发现编码问题，需要修复")
    
    # 清理测试文件
    cleanup_test_files()

if __name__ == "__main__":
    main()
