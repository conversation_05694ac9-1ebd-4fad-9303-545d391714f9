#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Tree控件的配置UI
支持层次化配置结构的可视化管理
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from typing import Dict, List, Optional, Any
import json
from pathlib import Path
from config_manager_v2 import ConfigManagerV2, ProfileMetadata

class ConfigTreeUI:
    """基于Tree控件的配置管理界面"""

    def __init__(self, parent=None):
        self.parent = parent
        self.config_manager = ConfigManagerV2()
        self.current_profile = None
        self.current_config = None

        # 创建主窗口
        if parent is None:
            self.root = ctk.CTk()
            self.root.title("WOW技能助手 - 配置管理")
            self.root.geometry("1200x800")
        else:
            self.root = parent

        self.setup_ui()
        self.load_profiles()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 创建顶部工具栏
        self.create_toolbar(main_frame)

        # 创建主要内容区域
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, pady=(10, 0))

        # 左侧：配置文件列表
        self.create_profile_panel(content_frame)

        # 右侧：配置详情树形视图
        self.create_tree_panel(content_frame)

        # 底部：状态栏
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ctk.CTkFrame(parent)
        toolbar.pack(fill="x", pady=(0, 10))

        # 配置文件操作按钮
        ctk.CTkButton(
            toolbar,
            text="新建配置",
            command=self.new_profile,
            width=100
        ).pack(side="left", padx=(10, 5), pady=10)

        ctk.CTkButton(
            toolbar,
            text="导入配置",
            command=self.import_profile,
            width=100
        ).pack(side="left", padx=5, pady=10)

        ctk.CTkButton(
            toolbar,
            text="导出配置",
            command=self.export_profile,
            width=100
        ).pack(side="left", padx=5, pady=10)

        ctk.CTkButton(
            toolbar,
            text="删除配置",
            command=self.delete_profile,
            width=100,
            fg_color="#D32F2F"
        ).pack(side="left", padx=5, pady=10)

        # 分隔符
        separator = ctk.CTkFrame(toolbar, width=2, height=30)
        separator.pack(side="left", padx=10, pady=10)

        # 技能操作按钮
        ctk.CTkButton(
            toolbar,
            text="添加技能",
            command=self.add_skill,
            width=100
        ).pack(side="left", padx=5, pady=10)

        ctk.CTkButton(
            toolbar,
            text="编辑技能",
            command=self.edit_skill,
            width=100
        ).pack(side="left", padx=5, pady=10)

        ctk.CTkButton(
            toolbar,
            text="删除技能",
            command=self.delete_skill,
            width=100,
            fg_color="#D32F2F"
        ).pack(side="left", padx=5, pady=10)

        # 右侧：搜索框
        search_frame = ctk.CTkFrame(toolbar)
        search_frame.pack(side="right", padx=10, pady=10)

        ctk.CTkLabel(search_frame, text="搜索:").pack(side="left", padx=(10, 5))

        self.search_var = tk.StringVar()
        self.search_var.trace("w", self.on_search_changed)
        search_entry = ctk.CTkEntry(
            search_frame,
            textvariable=self.search_var,
            placeholder_text="搜索技能或配置...",
            width=200
        )
        search_entry.pack(side="left", padx=(0, 10))

    def create_profile_panel(self, parent):
        """创建配置文件面板"""
        # 左侧框架
        left_frame = ctk.CTkFrame(parent)
        left_frame.pack(side="left", fill="y", padx=(0, 5))

        # 配置文件列表标题
        title_frame = ctk.CTkFrame(left_frame)
        title_frame.pack(fill="x", padx=10, pady=(10, 5))

        ctk.CTkLabel(
            title_frame,
            text="配置文件列表",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)

        # 配置文件列表
        list_frame = ctk.CTkFrame(left_frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # 使用Treeview显示配置文件
        self.profile_tree = ttk.Treeview(
            list_frame,
            columns=("class", "spec", "skills"),
            show="tree headings",
            height=15
        )

        # 设置列标题
        self.profile_tree.heading("#0", text="配置名称")
        self.profile_tree.heading("class", text="职业")
        self.profile_tree.heading("spec", text="专精")
        self.profile_tree.heading("skills", text="技能数")

        # 设置列宽
        self.profile_tree.column("#0", width=200)
        self.profile_tree.column("class", width=80)
        self.profile_tree.column("spec", width=80)
        self.profile_tree.column("skills", width=60)

        # 绑定选择事件
        self.profile_tree.bind("<<TreeviewSelect>>", self.on_profile_selected)

        # 添加滚动条
        profile_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.profile_tree.yview)
        self.profile_tree.configure(yscrollcommand=profile_scrollbar.set)

        self.profile_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        profile_scrollbar.pack(side="right", fill="y", pady=10)

    def create_tree_panel(self, parent):
        """创建配置详情树形面板"""
        # 右侧框架
        right_frame = ctk.CTkFrame(parent)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

        # 配置详情标题
        title_frame = ctk.CTkFrame(right_frame)
        title_frame.pack(fill="x", padx=10, pady=(10, 5))

        self.detail_title = ctk.CTkLabel(
            title_frame,
            text="请选择一个配置文件",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.detail_title.pack(pady=10)

        # 配置详情树形视图
        tree_frame = ctk.CTkFrame(right_frame)
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # 创建配置树形视图
        self.config_tree = ttk.Treeview(
            tree_frame,
            columns=("value", "type", "description"),
            show="tree headings"
        )

        # 设置列标题
        self.config_tree.heading("#0", text="配置项")
        self.config_tree.heading("value", text="值")
        self.config_tree.heading("type", text="类型")
        self.config_tree.heading("description", text="描述")

        # 设置列宽
        self.config_tree.column("#0", width=250)
        self.config_tree.column("value", width=200)
        self.config_tree.column("type", width=100)
        self.config_tree.column("description", width=300)

        # 绑定双击事件
        self.config_tree.bind("<Double-1>", self.on_tree_double_click)
        self.config_tree.bind("<Button-3>", self.on_tree_right_click)  # 右键菜单

        # 添加滚动条
        tree_scrollbar_y = ttk.Scrollbar(tree_frame, orient="vertical", command=self.config_tree.yview)
        tree_scrollbar_x = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.config_tree.xview)
        self.config_tree.configure(yscrollcommand=tree_scrollbar_y.set, xscrollcommand=tree_scrollbar_x.set)

        self.config_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        tree_scrollbar_y.pack(side="right", fill="y", pady=10)
        tree_scrollbar_x.pack(side="bottom", fill="x", padx=(10, 0))

    def create_status_bar(self, parent):
        """创建状态栏"""
        self.status_bar = ctk.CTkLabel(
            parent,
            text="就绪",
            anchor="w"
        )
        self.status_bar.pack(fill="x", pady=(10, 0))

    def load_profiles(self):
        """加载所有配置文件到列表"""
        try:
            # 清空现有项目
            for item in self.profile_tree.get_children():
                self.profile_tree.delete(item)

            # 获取所有配置文件
            profiles = self.config_manager.list_profiles()

            for profile in profiles:
                # 加载配置以获取技能数量
                config = self.config_manager.load_profile(profile["id"])
                skill_count = len(config.get("skills", {})) if config else 0

                # 插入到树形视图
                self.profile_tree.insert(
                    "", "end",
                    iid=profile["id"],
                    text=profile["name"],
                    values=(
                        profile.get("class", "未知"),
                        profile.get("spec", "未知"),
                        skill_count
                    )
                )

            self.update_status(f"已加载 {len(profiles)} 个配置文件")

        except Exception as e:
            self.update_status(f"加载配置文件失败: {str(e)}")

    def on_profile_selected(self, event):
        """配置文件选择事件"""
        selection = self.profile_tree.selection()
        if not selection:
            return

        profile_id = selection[0]
        self.load_profile_config(profile_id)

    def load_profile_config(self, profile_id: str):
        """加载并显示配置详情"""
        try:
            # 加载配置
            config = self.config_manager.load_profile(profile_id)
            if not config:
                self.update_status(f"无法加载配置: {profile_id}")
                return

            self.current_profile = profile_id
            self.current_config = config

            # 更新标题
            profile_name = config.get("metadata", {}).get("name", profile_id)
            self.detail_title.configure(text=f"配置详情: {profile_name}")

            # 构建配置树
            self.build_config_tree(config)

            self.update_status(f"已加载配置: {profile_name}")

        except Exception as e:
            self.update_status(f"加载配置详情失败: {str(e)}")

    def build_config_tree(self, config: dict):
        """构建配置树形视图"""
        # 清空现有项目
        for item in self.config_tree.get_children():
            self.config_tree.delete(item)

        # 1. 元数据节点
        metadata_node = self.config_tree.insert(
            "", "end",
            text="📋 元数据",
            values=("", "metadata", "配置文件基本信息"),
            open=True
        )

        metadata = config.get("metadata", {})
        metadata_items = [
            ("name", "配置名称", metadata.get("name", "")),
            ("description", "描述", metadata.get("description", "")),
            ("class_name", "职业", metadata.get("class_name", "")),
            ("spec_name", "专精", metadata.get("spec_name", "")),
            ("version", "版本", metadata.get("version", "")),
            ("author", "作者", metadata.get("author", "")),
            ("created_at", "创建时间", metadata.get("created_at", "")),
            ("updated_at", "更新时间", metadata.get("updated_at", "")),
        ]

        for key, name, value in metadata_items:
            self.config_tree.insert(
                metadata_node, "end",
                text=name,
                values=(str(value), "string", f"元数据.{key}"),
                tags=("metadata",)
            )

        # 标签
        tags = metadata.get("tags", [])
        if tags:
            tags_node = self.config_tree.insert(
                metadata_node, "end",
                text="标签",
                values=(", ".join(tags), "list", "配置标签"),
                tags=("metadata",)
            )

        # 2. 监控配置节点
        monitor_node = self.config_tree.insert(
            "", "end",
            text="🎯 监控配置",
            values=("", "monitor", "监控区域和相关设置"),
            open=True
        )

        monitor = config.get("monitor", {})
        region = monitor.get("region", {})

        region_node = self.config_tree.insert(
            monitor_node, "end",
            text="监控区域",
            values=("", "region", "Hekili建议区域坐标"),
            tags=("monitor",)
        )

        region_items = [
            ("x", "X坐标", region.get("x", 0)),
            ("y", "Y坐标", region.get("y", 0)),
            ("width", "宽度", region.get("width", 50)),
            ("height", "高度", region.get("height", 50)),
        ]

        for key, name, value in region_items:
            self.config_tree.insert(
                region_node, "end",
                text=name,
                values=(str(value), "integer", f"monitor.region.{key}"),
                tags=("monitor", "editable")
            )

        # 监控开关
        self.config_tree.insert(
            monitor_node, "end",
            text="启用监控",
            values=(str(monitor.get("enabled", True)), "boolean", "monitor.enabled"),
            tags=("monitor", "editable")
        )

        # 3. 技能配置节点
        skills_node = self.config_tree.insert(
            "", "end",
            text="⚔️ 技能配置",
            values=("", "skills", "技能绑定和分类设置"),
            open=True
        )

        # 技能分类
        categories = config.get("categories", {})
        if categories:
            categories_node = self.config_tree.insert(
                skills_node, "end",
                text="技能分类",
                values=(f"{len(categories)} 个分类", "categories", "技能分类定义"),
                tags=("skills",)
            )

            for cat_name, cat_info in categories.items():
                cat_node = self.config_tree.insert(
                    categories_node, "end",
                    text=cat_name,
                    values=(cat_info.get("color", ""), "category", cat_info.get("description", "")),
                    tags=("skills", "category")
                )

        # 技能列表
        skills = config.get("skills", {})
        if skills:
            # 按分类组织技能
            skills_by_category = {}
            for skill_id, skill_info in skills.items():
                category = skill_info.get("category", "未分类")
                if category not in skills_by_category:
                    skills_by_category[category] = []
                skills_by_category[category].append((skill_id, skill_info))

            for category, skill_list in skills_by_category.items():
                category_node = self.config_tree.insert(
                    skills_node, "end",
                    text=f"📁 {category}",
                    values=(f"{len(skill_list)} 个技能", "skill_category", f"{category}分类的技能"),
                    tags=("skills",),
                    open=True
                )

                for skill_id, skill_info in skill_list:
                    skill_node = self.config_tree.insert(
                        category_node, "end",
                        text=f"🎯 {skill_info.get('name', skill_id)}",
                        values=(
                            skill_info.get('hotkey', ''),
                            "skill",
                            skill_info.get('description', '')
                        ),
                        tags=("skills", "skill", "editable")
                    )

                    # 技能详细属性
                    skill_properties = [
                        ("hotkey", "快捷键", skill_info.get("hotkey", "")),
                        ("priority", "优先级", skill_info.get("priority", 1)),
                        ("cooldown", "冷却时间", skill_info.get("cooldown", 0.5)),
                        ("threshold", "匹配阈值", skill_info.get("threshold", 0.9)),
                        ("enabled", "启用状态", skill_info.get("enabled", True)),
                        ("template", "模板文件", skill_info.get("template", "")),
                    ]

                    for prop_key, prop_name, prop_value in skill_properties:
                        prop_type = "boolean" if isinstance(prop_value, bool) else \
                                   "float" if isinstance(prop_value, float) else \
                                   "integer" if isinstance(prop_value, int) else "string"

                        self.config_tree.insert(
                            skill_node, "end",
                            text=prop_name,
                            values=(str(prop_value), prop_type, f"skills.{skill_id}.{prop_key}"),
                            tags=("skills", "skill_property", "editable")
                        )

                    # 技能统计
                    stats = skill_info.get("stats", {})
                    if stats:
                        stats_node = self.config_tree.insert(
                            skill_node, "end",
                            text="📊 使用统计",
                            values=("", "stats", "技能使用统计信息"),
                            tags=("skills", "stats")
                        )

                        stats_items = [
                            ("usage_count", "使用次数", stats.get("usage_count", 0)),
                            ("avg_similarity", "平均匹配度", f"{stats.get('avg_similarity', 0):.2%}"),
                            ("last_used", "最后使用", stats.get("last_used", "")),
                        ]

                        for stat_key, stat_name, stat_value in stats_items:
                            self.config_tree.insert(
                                stats_node, "end",
                                text=stat_name,
                                values=(str(stat_value), "readonly", f"skills.{skill_id}.stats.{stat_key}"),
                                tags=("skills", "stats", "readonly")
                            )

        # 4. 设置配置节点
        settings_node = self.config_tree.insert(
            "", "end",
            text="⚙️ 设置配置",
            values=("", "settings", "检测、输入和性能设置"),
            open=True
        )

        settings = config.get("settings", {})

        # 检测设置
        detection = settings.get("detection", {})
        if detection:
            detection_node = self.config_tree.insert(
                settings_node, "end",
                text="🔍 检测设置",
                values=("", "detection", "图标检测相关设置"),
                tags=("settings",),
                open=True
            )

            detection_items = [
                ("scan_interval", "扫描间隔(秒)", detection.get("scan_interval", 0.33)),
                ("threshold", "匹配阈值", detection.get("threshold", 0.9)),
                ("auto_add_skills", "自动添加技能", detection.get("auto_add_skills", True)),
                ("detection_method", "检测方法", detection.get("detection_method", "hash_comparison")),
            ]

            for key, name, value in detection_items:
                prop_type = "boolean" if isinstance(value, bool) else \
                           "float" if isinstance(value, float) else "string"

                self.config_tree.insert(
                    detection_node, "end",
                    text=name,
                    values=(str(value), prop_type, f"settings.detection.{key}"),
                    tags=("settings", "detection", "editable")
                )

        # 输入设置
        input_settings = settings.get("input", {})
        if input_settings:
            input_node = self.config_tree.insert(
                settings_node, "end",
                text="⌨️ 输入设置",
                values=("", "input", "按键和输入相关设置"),
                tags=("settings",),
                open=True
            )

            input_items = [
                ("key_press_delay", "按键延迟(秒)", input_settings.get("key_press_delay", 0.19)),
                ("monitor_hotkey", "监控热键", input_settings.get("monitor_hotkey", "`")),
                ("enable_combos", "启用连招", input_settings.get("enable_combos", True)),
                ("smart_casting", "智能施法", input_settings.get("smart_casting", True)),
            ]

            for key, name, value in input_items:
                prop_type = "boolean" if isinstance(value, bool) else \
                           "float" if isinstance(value, float) else "string"

                self.config_tree.insert(
                    input_node, "end",
                    text=name,
                    values=(str(value), prop_type, f"settings.input.{key}"),
                    tags=("settings", "input", "editable")
                )

    def on_tree_double_click(self, event):
        """树形视图双击事件"""
        item = self.config_tree.selection()[0] if self.config_tree.selection() else None
        if not item:
            return

        # 获取项目信息
        item_data = self.config_tree.item(item)
        tags = item_data.get("tags", [])

        # 只有可编辑的项目才能双击编辑
        if "editable" in tags:
            self.edit_tree_item(item)

    def on_tree_right_click(self, event):
        """树形视图右键菜单"""
        item = self.config_tree.identify_row(event.y)
        if not item:
            return

        self.config_tree.selection_set(item)

        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)

        item_data = self.config_tree.item(item)
        tags = item_data.get("tags", [])

        if "editable" in tags:
            context_menu.add_command(label="编辑", command=lambda: self.edit_tree_item(item))

        if "skill" in tags:
            context_menu.add_separator()
            context_menu.add_command(label="复制技能", command=lambda: self.copy_skill(item))
            context_menu.add_command(label="删除技能", command=lambda: self.delete_skill_from_tree(item))

        if "category" in tags:
            context_menu.add_command(label="添加技能到此分类", command=lambda: self.add_skill_to_category(item))

        context_menu.add_separator()
        context_menu.add_command(label="展开所有", command=lambda: self.expand_all(item))
        context_menu.add_command(label="折叠所有", command=lambda: self.collapse_all(item))

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_tree_item(self, item):
        """编辑树形视图项目"""
        item_data = self.config_tree.item(item)
        current_value = item_data["values"][0] if item_data["values"] else ""
        value_type = item_data["values"][1] if len(item_data["values"]) > 1 else "string"
        path = item_data["values"][2] if len(item_data["values"]) > 2 else ""

        # 创建编辑对话框
        dialog = EditDialog(self.root, item_data["text"], current_value, value_type)
        new_value = dialog.get_result()

        if new_value is not None:
            # 更新配置
            self.update_config_value(path, new_value)

            # 更新树形视图
            values = list(item_data["values"])
            values[0] = str(new_value)
            self.config_tree.item(item, values=values)

            self.update_status(f"已更新: {item_data['text']} = {new_value}")

    def update_config_value(self, path: str, value):
        """更新配置值"""
        if not self.current_config or not path:
            return

        # 解析路径
        parts = path.split(".")
        current = self.current_config

        # 导航到目标位置
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]

        # 设置值
        current[parts[-1]] = value

        # 保存配置
        if self.current_profile:
            self.config_manager.save_profile(self.current_profile, self.current_config)

    def on_search_changed(self, *args):
        """搜索框内容变化事件"""
        search_text = self.search_var.get().lower()

        if not search_text:
            # 显示所有项目
            self.show_all_items()
        else:
            # 过滤显示
            self.filter_items(search_text)

    def filter_items(self, search_text: str):
        """过滤显示项目"""
        def check_item(item):
            item_data = self.config_tree.item(item)
            text = item_data["text"].lower()
            values = " ".join(str(v).lower() for v in item_data["values"])

            # 检查当前项目是否匹配
            matches = search_text in text or search_text in values

            # 检查子项目
            children = self.config_tree.get_children(item)
            child_matches = any(check_item(child) for child in children)

            # 如果当前项目或子项目匹配，则显示
            if matches or child_matches:
                self.config_tree.item(item, open=True)
                return True
            else:
                return False

        # 检查所有顶级项目
        for item in self.config_tree.get_children():
            check_item(item)

    def show_all_items(self):
        """显示所有项目"""
        def show_item(item):
            children = self.config_tree.get_children(item)
            for child in children:
                show_item(child)

        for item in self.config_tree.get_children():
            show_item(item)

    def expand_all(self, item=None):
        """展开所有项目"""
        if item is None:
            items = self.config_tree.get_children()
        else:
            items = [item]

        for item in items:
            self.config_tree.item(item, open=True)
            children = self.config_tree.get_children(item)
            for child in children:
                self.expand_all(child)

    def collapse_all(self, item=None):
        """折叠所有项目"""
        if item is None:
            items = self.config_tree.get_children()
        else:
            items = [item]

        for item in items:
            self.config_tree.item(item, open=False)
            children = self.config_tree.get_children(item)
            for child in children:
                self.collapse_all(child)

    def update_status(self, message: str):
        """更新状态栏"""
        self.status_bar.configure(text=message)
        print(f"状态: {message}")  # 同时输出到控制台

    # 工具栏功能实现
    def new_profile(self):
        """新建配置文件"""
        dialog = ProfileDialog(self.root)
        result = dialog.get_result()

        if result:
            profile_id, metadata = result
            if self.config_manager.create_profile(profile_id, metadata):
                self.load_profiles()
                self.update_status(f"已创建新配置: {metadata.name}")
            else:
                self.update_status("创建配置失败")

    def import_profile(self):
        """导入配置文件"""
        # 这里可以实现从文件导入配置的功能
        self.update_status("导入功能待实现")

    def export_profile(self):
        """导出配置文件"""
        if not self.current_profile:
            self.update_status("请先选择一个配置")
            return

        # 这里可以实现导出配置到文件的功能
        self.update_status("导出功能待实现")

    def delete_profile(self):
        """删除配置文件"""
        if not self.current_profile:
            self.update_status("请先选择一个配置")
            return

        # 确认删除
        import tkinter.messagebox as msgbox
        if msgbox.askyesno("确认删除", f"确定要删除配置 '{self.current_profile}' 吗？\n此操作不可撤销！"):
            if self.config_manager.delete_profile(self.current_profile):
                self.load_profiles()
                self.current_profile = None
                self.current_config = None
                self.detail_title.configure(text="请选择一个配置文件")
                # 清空配置树
                for item in self.config_tree.get_children():
                    self.config_tree.delete(item)
                self.update_status("配置已删除")
            else:
                self.update_status("删除配置失败")

    def add_skill(self):
        """添加技能"""
        if not self.current_config:
            self.update_status("请先选择一个配置")
            return

        dialog = SkillDialog(self.root, self.current_config.get("categories", {}))
        result = dialog.get_result()

        if result:
            skill_id, skill_config = result
            # 添加到配置
            if "skills" not in self.current_config:
                self.current_config["skills"] = {}

            self.current_config["skills"][skill_id] = skill_config

            # 保存配置
            if self.config_manager.save_profile(self.current_profile, self.current_config):
                # 重新构建树形视图
                self.build_config_tree(self.current_config)
                self.update_status(f"已添加技能: {skill_config['name']}")
            else:
                self.update_status("保存技能失败")

    def edit_skill(self):
        """编辑技能"""
        selection = self.config_tree.selection()
        if not selection:
            self.update_status("请先选择一个技能")
            return

        item = selection[0]
        item_data = self.config_tree.item(item)
        tags = item_data.get("tags", [])

        if "skill" not in tags:
            self.update_status("请选择一个技能项目")
            return

        # 这里可以实现技能编辑功能
        self.update_status("技能编辑功能待实现")

    def delete_skill(self):
        """删除技能"""
        selection = self.config_tree.selection()
        if not selection:
            self.update_status("请先选择一个技能")
            return

        item = selection[0]
        item_data = self.config_tree.item(item)
        tags = item_data.get("tags", [])

        if "skill" not in tags:
            self.update_status("请选择一个技能项目")
            return

        # 这里可以实现技能删除功能
        self.update_status("技能删除功能待实现")

    def copy_skill(self, item):
        """复制技能"""
        self.update_status("复制技能功能待实现")

    def delete_skill_from_tree(self, item):
        """从树形视图删除技能"""
        self.update_status("删除技能功能待实现")

    def add_skill_to_category(self, item):
        """添加技能到分类"""
        self.update_status("添加技能到分类功能待实现")


class EditDialog:
    """编辑对话框"""

    def __init__(self, parent, title: str, current_value: str, value_type: str):
        self.result = None

        # 创建对话框窗口
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title(f"编辑 - {title}")
        self.dialog.geometry("400x200")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (200 // 2)
        self.dialog.geometry(f"400x200+{x}+{y}")

        # 创建界面
        self.setup_ui(title, current_value, value_type)

        # 等待用户操作
        self.dialog.wait_window()

    def setup_ui(self, title: str, current_value: str, value_type: str):
        """设置对话框界面"""
        # 标题
        title_label = ctk.CTkLabel(
            self.dialog,
            text=f"编辑: {title}",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=20)

        # 输入框
        self.value_var = tk.StringVar(value=current_value)

        if value_type == "boolean":
            # 布尔值使用开关
            self.value_widget = ctk.CTkSwitch(
                self.dialog,
                text="启用",
                variable=self.value_var,
                onvalue="True",
                offvalue="False"
            )
            if current_value.lower() == "true":
                self.value_widget.select()
        else:
            # 其他类型使用文本框
            self.value_widget = ctk.CTkEntry(
                self.dialog,
                textvariable=self.value_var,
                width=300
            )

        self.value_widget.pack(pady=20)

        # 按钮
        button_frame = ctk.CTkFrame(self.dialog)
        button_frame.pack(pady=20)

        ctk.CTkButton(
            button_frame,
            text="确定",
            command=self.on_ok,
            width=100
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            button_frame,
            text="取消",
            command=self.on_cancel,
            width=100
        ).pack(side="left", padx=10)

    def on_ok(self):
        """确定按钮"""
        value = self.value_var.get()

        # 类型转换
        try:
            if value.lower() in ("true", "false"):
                self.result = value.lower() == "true"
            elif value.replace(".", "").isdigit():
                if "." in value:
                    self.result = float(value)
                else:
                    self.result = int(value)
            else:
                self.result = value
        except:
            self.result = value

        self.dialog.destroy()

    def on_cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()

    def get_result(self):
        """获取结果"""
        return self.result


class ProfileDialog:
    """配置文件创建对话框"""

    def __init__(self, parent):
        self.result = None

        # 创建对话框窗口
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("新建配置文件")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")

        self.setup_ui()
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        # 标题
        title_label = ctk.CTkLabel(
            self.dialog,
            text="创建新的配置文件",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=20)

        # 表单
        form_frame = ctk.CTkFrame(self.dialog)
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 配置ID
        ctk.CTkLabel(form_frame, text="配置ID:").pack(anchor="w", padx=20, pady=(20, 5))
        self.id_var = tk.StringVar()
        ctk.CTkEntry(form_frame, textvariable=self.id_var, width=400).pack(padx=20, pady=(0, 10))

        # 配置名称
        ctk.CTkLabel(form_frame, text="配置名称:").pack(anchor="w", padx=20, pady=(10, 5))
        self.name_var = tk.StringVar()
        ctk.CTkEntry(form_frame, textvariable=self.name_var, width=400).pack(padx=20, pady=(0, 10))

        # 描述
        ctk.CTkLabel(form_frame, text="描述:").pack(anchor="w", padx=20, pady=(10, 5))
        self.desc_var = tk.StringVar()
        ctk.CTkEntry(form_frame, textvariable=self.desc_var, width=400).pack(padx=20, pady=(0, 10))

        # 职业
        ctk.CTkLabel(form_frame, text="职业:").pack(anchor="w", padx=20, pady=(10, 5))
        self.class_var = tk.StringVar()
        class_combo = ctk.CTkComboBox(
            form_frame,
            variable=self.class_var,
            values=["法师", "战士", "圣骑士", "死亡骑士", "猎人", "盗贼", "牧师", "萨满", "术士", "德鲁伊", "武僧", "恶魔猎手"],
            width=400
        )
        class_combo.pack(padx=20, pady=(0, 10))

        # 专精
        ctk.CTkLabel(form_frame, text="专精:").pack(anchor="w", padx=20, pady=(10, 5))
        self.spec_var = tk.StringVar()
        ctk.CTkEntry(form_frame, textvariable=self.spec_var, width=400).pack(padx=20, pady=(0, 20))

        # 按钮
        button_frame = ctk.CTkFrame(form_frame)
        button_frame.pack(pady=20)

        ctk.CTkButton(
            button_frame,
            text="创建",
            command=self.on_create,
            width=120
        ).pack(side="left", padx=10)

        ctk.CTkButton(
            button_frame,
            text="取消",
            command=self.on_cancel,
            width=120
        ).pack(side="left", padx=10)

    def on_create(self):
        """创建按钮"""
        profile_id = self.id_var.get().strip()
        name = self.name_var.get().strip()

        if not profile_id or not name:
            import tkinter.messagebox as msgbox
            msgbox.showerror("错误", "请填写配置ID和名称")
            return

        metadata = ProfileMetadata(
            name=name,
            description=self.desc_var.get().strip(),
            class_name=self.class_var.get().strip(),
            spec_name=self.spec_var.get().strip()
        )

        self.result = (profile_id, metadata)
        self.dialog.destroy()

    def on_cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()

    def get_result(self):
        """获取结果"""
        return self.result


class SkillDialog:
    """技能创建对话框"""

    def __init__(self, parent, categories: dict):
        self.result = None
        self.categories = categories

        # 创建对话框窗口
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("添加技能")
        self.dialog.geometry("500x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"500x500+{x}+{y}")

        self.setup_ui()
        self.dialog.wait_window()

    def setup_ui(self):
        """设置界面"""
        # 这里可以实现技能添加对话框的详细界面
        # 为了简化，暂时只显示一个占位符
        ctk.CTkLabel(
            self.dialog,
            text="技能添加对话框\n(待实现详细界面)",
            font=ctk.CTkFont(size=16)
        ).pack(expand=True)

        ctk.CTkButton(
            self.dialog,
            text="关闭",
            command=self.on_cancel
        ).pack(pady=20)

    def on_cancel(self):
        """取消按钮"""
        self.result = None
        self.dialog.destroy()

    def get_result(self):
        """获取结果"""
        return self.result


# 主程序入口
if __name__ == "__main__":
    try:
        app = ConfigTreeUI()
        if hasattr(app, 'root') and app.root:
            app.root.mainloop()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()