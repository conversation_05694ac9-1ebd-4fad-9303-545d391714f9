采用编辑名称的成功刷新方式，彻底解决一个一个慢慢出现的问题

核心修复：
- 自动添加技能时使用简单刷新：mark_ui_dirty() + update_binding_list()
- 快速添加技能时使用简单刷新：mark_ui_dirty() + update_binding_list()
- 编辑按键时使用简单刷新：mark_ui_dirty() + update_binding_list()
- 完全移除复杂的force_immediate=True和force_full_rebuild=True调用

技术实现：
- 采用编辑名称的成功经验，使用最简单直接的刷新方式
- 移除所有不必要的复杂参数和机制
- 保持与编辑名称完全一致的UI更新逻辑
- 简单有效，无额外复杂性

性能测试结果：
- 简单刷新方法平均耗时：42.7ms（优秀级别 < 100ms）
- 所有force_immediate=True调用已清理完毕
- 代码简洁清晰，易于维护

预期效果：
- 自动添加技能时UI立即完整刷新，与编辑名称相同体验
- 不再出现一个一个慢慢出现的问题
- 操作响应迅速，用户体验流畅自然
