#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技能删除功能测试脚本
用于验证技能删除功能是否正常工作
"""

import os
import sys
import json

def test_delete_functionality():
    """测试删除功能"""
    print("=== 技能删除功能测试 ===")
    
    try:
        # 导入主程序模块
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 初始化成功")
        
        # 测试配置加载
        config_dir = "configs"
        config_files = [f[:-5] for f in os.listdir(config_dir) if f.endswith('.json')]
        
        if not config_files:
            print("❌ 没有找到配置文件")
            return False
        
        # 选择一个有技能的配置进行测试
        test_spec = None
        for spec in config_files:
            if processor.load_config(spec):
                if len(processor.icon_bindings) > 0:
                    test_spec = spec
                    break
        
        if not test_spec:
            print("❌ 没有找到包含技能的配置")
            return False
        
        print(f"📝 使用配置进行测试: {test_spec}")
        print(f"   - 技能绑定数量: {len(processor.icon_bindings)}")
        
        # 显示所有技能
        print("   - 当前技能列表:")
        for name, binding in processor.icon_bindings.items():
            print(f"     * {binding.text} ({name}) -> {binding.hotkey}")
        
        # 测试删除功能
        if len(processor.icon_bindings) > 0:
            # 选择第一个技能进行删除测试
            first_binding_name = list(processor.icon_bindings.keys())[0]
            first_binding = processor.icon_bindings[first_binding_name]
            
            print(f"\n🗑️  测试删除技能: {first_binding.text} ({first_binding_name})")
            
            # 记录删除前的数量
            before_count = len(processor.icon_bindings)
            
            # 执行删除
            result = processor.remove_icon_binding(first_binding_name)
            
            # 检查删除结果
            after_count = len(processor.icon_bindings)
            
            if result and after_count == before_count - 1:
                print(f"✅ 删除成功！技能数量从 {before_count} 减少到 {after_count}")
                
                # 验证技能确实被删除
                if first_binding_name not in processor.icon_bindings:
                    print(f"✅ 确认技能 {first_binding_name} 已从内存中删除")
                else:
                    print(f"❌ 技能 {first_binding_name} 仍在内存中")
                    return False
                
                # 测试保存配置
                if processor.save_config(test_spec):
                    print(f"✅ 配置保存成功")
                    
                    # 重新加载配置验证删除是否持久化
                    processor.load_config(test_spec)
                    if first_binding_name not in processor.icon_bindings:
                        print(f"✅ 确认删除已持久化到配置文件")
                    else:
                        print(f"❌ 删除未持久化，技能仍在配置文件中")
                        return False
                else:
                    print(f"❌ 配置保存失败")
                    return False
                
            else:
                print(f"❌ 删除失败！返回值: {result}, 技能数量: {before_count} -> {after_count}")
                return False
        
        # 测试删除不存在的技能
        print(f"\n🧪 测试删除不存在的技能")
        result = processor.remove_icon_binding("non_existent_skill")
        if not result:
            print(f"✅ 正确处理了不存在的技能删除请求")
        else:
            print(f"❌ 删除不存在的技能时返回了错误的结果")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_integration():
    """测试UI集成"""
    print("\n=== UI集成测试 ===")
    
    try:
        # 检查UI相关的方法
        print("📝 检查UI相关方法...")
        
        # 这里我们只能检查方法是否存在，不能实际运行UI
        from XXD import WoWSkillAssistant
        
        # 检查关键方法是否存在
        methods_to_check = [
            'remove_binding',
            'show_edit_menu', 
            'mark_ui_dirty',
            'update_binding_list'
        ]
        
        for method_name in methods_to_check:
            if hasattr(WoWSkillAssistant, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
                return False
        
        print("✅ 所有UI方法检查通过")
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("WOW技能助手 - 技能删除功能测试")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("skill_processor.py"):
        print("❌ 请在WOW项目目录中运行此测试")
        return
    
    # 运行测试
    test1_result = test_delete_functionality()
    test2_result = test_ui_integration()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"删除功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"UI集成测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！技能删除功能应该正常工作。")
        print("\n💡 使用说明:")
        print("1. 在技能列表中双击任意技能")
        print("2. 在弹出菜单中选择'删除'")
        print("3. 技能将被删除并自动保存配置")
    else:
        print("\n⚠️  部分测试失败，请检查代码修复。")

if __name__ == "__main__":
    main()
