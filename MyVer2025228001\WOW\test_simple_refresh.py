#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单刷新测试脚本
验证使用与编辑名称相同的简单刷新方式是否解决了一个一个慢慢出现的问题
"""

import os
import sys
import json
import time
import numpy as np
import cv2

def test_simple_refresh_approach():
    """测试简单刷新方法"""
    print("=== 简单刷新方法测试 ===")
    
    try:
        # 导入主程序模块
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 初始化成功")
        
        # 测试配置加载
        config_dir = "configs"
        config_files = [f[:-5] for f in os.listdir(config_dir) if f.endswith('.json')]
        
        if not config_files:
            print("❌ 没有找到配置文件")
            return False
        
        # 选择一个配置进行测试
        test_spec = config_files[0]
        print(f"📝 使用配置进行测试: {test_spec}")
        
        if processor.load_config(test_spec):
            initial_count = len(processor.icon_bindings)
            print(f"   - 初始技能数量: {initial_count}")
            
            # 测试简单刷新方法的性能
            print("\n🔄 测试简单刷新方法...")
            
            # 连续添加多个技能，模拟自动添加场景
            skills_to_add = [
                {"name": "simple_test_1", "text": "简单测试1", "hotkey": "s1"},
                {"name": "simple_test_2", "text": "简单测试2", "hotkey": "s2"},
                {"name": "simple_test_3", "text": "简单测试3", "hotkey": "s3"}
            ]
            
            total_start_time = time.time()
            
            for i, skill_info in enumerate(skills_to_add):
                print(f"\n--- 添加第 {i+1}/3 个技能 ---")
                
                # 创建模拟的技能图标模板
                template = np.random.randint(0, 255, (48, 48, 3), dtype=np.uint8)
                
                # 确保技能名称唯一
                skill_count = len(processor.icon_bindings) + 1
                while skill_info["name"] in processor.icon_bindings:
                    skill_count += 1
                    skill_info["name"] = f"simple_test_{skill_count}"
                    skill_info["text"] = f"简单测试{skill_count}"
                
                print(f"📝 添加技能: {skill_info['text']}")
                
                # 记录单个技能处理开始时间
                single_start = time.time()
                
                # 1. 添加技能绑定
                binding = processor.add_icon_binding(
                    name=skill_info["name"],
                    text=skill_info["text"],
                    hotkey=skill_info["hotkey"],
                    template_image=template
                )
                
                if binding:
                    print(f"✅ 技能绑定创建: {binding.text} -> {binding.hotkey}")
                    
                    # 2. 保存配置
                    if processor.save_config(test_spec):
                        print(f"💾 配置保存成功")
                        
                        # 3. 验证技能数量
                        current_count = len(processor.icon_bindings)
                        expected_count = initial_count + i + 1
                        if current_count == expected_count:
                            print(f"📊 技能数量正确: {current_count}")
                        else:
                            print(f"❌ 技能数量错误: 期望 {expected_count}, 实际 {current_count}")
                            return False
                    else:
                        print(f"❌ 配置保存失败")
                        return False
                else:
                    print(f"❌ 技能绑定创建失败")
                    return False
                
                single_end = time.time()
                single_duration = (single_end - single_start) * 1000
                print(f"⏱️  单个技能处理耗时: {single_duration:.1f}ms")
            
            total_end_time = time.time()
            total_duration = (total_end_time - total_start_time) * 1000
            avg_duration = total_duration / len(skills_to_add)
            
            print(f"\n📊 简单刷新方法统计:")
            print(f"   - 总处理时间: {total_duration:.1f}ms")
            print(f"   - 平均每个技能: {avg_duration:.1f}ms")
            print(f"   - 最终技能数量: {len(processor.icon_bindings)}")
            print(f"   - 新增技能数量: {len(processor.icon_bindings) - initial_count}")
            
            # 性能评估
            if avg_duration < 100:
                print(f"🎉 简单刷新方法性能优秀: 平均 {avg_duration:.1f}ms < 100ms")
                performance_rating = "优秀"
            elif avg_duration < 200:
                print(f"✅ 简单刷新方法性能良好: 平均 {avg_duration:.1f}ms < 200ms")
                performance_rating = "良好"
            else:
                print(f"⚠️  简单刷新方法性能一般: 平均 {avg_duration:.1f}ms >= 200ms")
                performance_rating = "一般"
            
            return performance_rating in ["优秀", "良好"]
        else:
            print(f"❌ 加载配置失败: {test_spec}")
            return False
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_simple_refresh_code():
    """验证简单刷新代码的实现"""
    print("\n=== 简单刷新代码验证 ===")
    
    try:
        # 检查代码中的简单刷新实现
        print("📝 检查简单刷新代码...")
        
        with open("XXD.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 验证关键修改点
        verification_points = [
            {
                "name": "自动添加简单刷新",
                "pattern": "自动添加技能，使用简单刷新",
                "description": "自动添加技能时使用简单刷新方式"
            },
            {
                "name": "快速添加简单刷新",
                "pattern": "快速添加技能，使用简单刷新",
                "description": "快速添加技能时使用简单刷新方式"
            },
            {
                "name": "编辑名称刷新方式",
                "pattern": "self.mark_ui_dirty()\n                        self.update_binding_list()",
                "description": "编辑名称后的刷新方式"
            }
        ]
        
        all_verified = True
        for point in verification_points:
            if point["pattern"] in content:
                print(f"✅ {point['name']}: {point['description']}")
            else:
                print(f"❌ {point['name']}: 未找到 - {point['description']}")
                all_verified = False
        
        # 检查是否移除了复杂的强制重建调用
        complex_patterns = [
            "force_full_rebuild=True",
            "force_immediate=True",
            "强制完全重建UI"
        ]
        
        print("\n📝 检查是否移除了复杂的强制重建...")
        for pattern in complex_patterns:
            count = content.count(pattern)
            if pattern == "force_full_rebuild=True":
                # 这个应该只在mark_ui_dirty方法中出现，不应该在添加技能时使用
                if count <= 2:  # mark_ui_dirty方法定义和初始化
                    print(f"✅ {pattern}: 已移除不必要的调用 (剩余{count}个)")
                else:
                    print(f"⚠️  {pattern}: 仍有过多调用 ({count}个)")
                    all_verified = False
            elif pattern == "force_immediate=True":
                # 这个应该只在编辑按键时使用
                if count <= 1:
                    print(f"✅ {pattern}: 已移除不必要的调用 (剩余{count}个)")
                else:
                    print(f"⚠️  {pattern}: 仍有过多调用 ({count}个)")
                    all_verified = False
            else:
                if count == 0:
                    print(f"✅ {pattern}: 已完全移除")
                else:
                    print(f"⚠️  {pattern}: 仍有调用 ({count}个)")
                    all_verified = False
        
        return all_verified
        
    except Exception as e:
        print(f"❌ 验证简单刷新代码时出错: {str(e)}")
        return False

def compare_refresh_methods():
    """对比不同刷新方法的特点"""
    print("\n=== 刷新方法对比分析 ===")
    
    try:
        print("📊 刷新方法对比:")
        print()
        
        methods = [
            {
                "name": "编辑名称刷新",
                "code": "self.mark_ui_dirty() + self.update_binding_list()",
                "特点": "简单直接，无额外参数",
                "效果": "立即刷新，无延迟感",
                "问题": "无"
            },
            {
                "name": "强制完全重建",
                "code": "mark_ui_dirty(force_full_rebuild=True) + update_binding_list(force_immediate=True)",
                "特点": "复杂机制，多个参数",
                "效果": "理论上应该立即刷新",
                "问题": "仍然一个一个慢慢出现"
            },
            {
                "name": "简单刷新（新方案）",
                "code": "self.mark_ui_dirty() + self.update_binding_list()",
                "特点": "采用编辑名称的成功方案",
                "效果": "预期立即刷新，无延迟感",
                "问题": "待验证"
            }
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"{i}. {method['name']}:")
            print(f"   代码: {method['code']}")
            print(f"   特点: {method['特点']}")
            print(f"   效果: {method['效果']}")
            print(f"   问题: {method['问题']}")
            print()
        
        print("🎯 分析结论:")
        print("- 编辑名称的刷新方式工作完美，无任何延迟")
        print("- 强制重建的复杂机制反而可能引入了问题")
        print("- 简单的方案往往是最有效的方案")
        print("- 应该采用编辑名称的成功经验")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比刷新方法时出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("WOW技能助手 - 简单刷新方法测试")
    print("=" * 60)
    print("🎯 目标: 采用编辑名称的成功刷新方式解决一个一个慢慢出现的问题")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("skill_processor.py"):
        print("❌ 请在WOW项目目录中运行此测试")
        return
    
    # 运行测试
    test1_result = test_simple_refresh_approach()
    test2_result = verify_simple_refresh_code()
    test3_result = compare_refresh_methods()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"简单刷新方法测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"简单刷新代码验证: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"刷新方法对比分析: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result and test2_result and test3_result:
        print("\n🎉 简单刷新方法测试通过！")
        print("\n✨ 修复策略:")
        print("1. ✅ 采用编辑名称的成功刷新方式")
        print("2. ✅ 移除复杂的强制重建机制")
        print("3. ✅ 使用简单直接的mark_ui_dirty() + update_binding_list()")
        print("4. ✅ 避免不必要的参数和复杂逻辑")
        
        print("\n🔧 技术实现:")
        print("- 自动添加技能: mark_ui_dirty() + update_binding_list()")
        print("- 快速添加技能: mark_ui_dirty() + update_binding_list()")
        print("- 与编辑名称保持一致的刷新方式")
        print("- 简单有效，无额外复杂性")
        
        print("\n🎮 预期效果:")
        print("- 自动添加技能时UI立即完整刷新")
        print("- 不再出现一个一个慢慢出现的问题")
        print("- 与编辑名称相同的流畅体验")
        print("- 简单可靠，易于维护")
        
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")
        
        if not test1_result:
            print("- 简单刷新方法性能需要优化")
        if not test2_result:
            print("- 简单刷新代码实现需要完善")
        if not test3_result:
            print("- 刷新方法对比分析有问题")

if __name__ == "__main__":
    main()
