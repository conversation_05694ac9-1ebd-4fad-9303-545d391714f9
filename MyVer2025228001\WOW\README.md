# 🎮 WOW技能助手 v2.0.0

[![Version](https://img.shields.io/badge/version-v2.0.0-gold.svg)](https://github.com/kookoo2024/My-WOW/releases)
[![Python](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)

一个用于World of Warcraft的智能技能释放助手，基于图像识别技术自动识别Hekili插件的技能建议并执行相应的按键操作。

## 🌟 v2.0.0 "黄金标题版" 重大更新

- ✨ **全新黄色粗体标题栏** - 程序状态一目了然
- 🎨 **优化界面布局** - 按钮排列更合理，界面更紧凑
- ⚡ **性能提升** - UI响应更流畅，图像处理更高效
- 🔧 **功能完善** - 完善配置管理，增强程序稳定性

## 🚀 主要功能

### 🎯 智能技能识别
- 基于OpenCV的高精度图像模板匹配
- 实时监控Hekili插件建议区域
- 支持多种技能图标识别

### ⚡ 自动技能释放
- 毫秒级响应速度
- 智能按键模拟
- 支持单键和组合键

### 🔧 多配置管理
- 支持创建、切换、删除多套配置
- 中文配置名称支持
- 配置文件自动保存

### 📊 实时状态显示
- 黄色粗体标题栏显示程序状态
- 实时显示配置名称和自动添加状态
- 按键记录实时更新

## 📦 安装和使用

### 系统要求
- **操作系统**: Windows 10 (1903+) / Windows 11
- **Python版本**: 3.8+ (推荐 3.10+)
- **内存**: 4GB+ (推荐 8GB+)
- **存储**: 100MB+ 可用空间

### 快速开始

1. **克隆仓库**
   ```bash
   git clone https://github.com/kookoo2024/My-WOW.git
   cd My-WOW/WOW
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动程序**
   ```bash
   python XXD.py
   ```

### 首次使用

1. **创建配置**: 点击"新建"按钮创建技能配置
2. **设置区域**: 点击"设区"按钮设置监控区域
3. **添加技能**: 使用F10快捷键添加技能绑定
4. **开始监控**: 点击"开始监控"开始自动技能释放

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| **`** (反引号) | 开始/停止监控 |
| **F9** | 切换自动添加技能开关 |
| **F10** | 快速添加技能绑定 |
| **F11** | 设置监控区域 |
| **F12** | 退出程序 |

## 📁 项目结构

```
WOW/
├── XXD.py                 # 主程序文件
├── skill_processor.py     # 技能处理核心模块
├── requirements.txt       # Python依赖包列表
├── README.md             # 项目说明文档
├── VERSION.md            # 版本信息
├── configs/              # 配置文件目录
│   ├── last_config.json # 最后使用的配置
│   └── *.json           # 用户配置文件
└── templates/            # 技能图标模板目录
    └── *.png            # 技能图标文件
```

## 🎨 界面预览

### 主界面布局
```
[黄色粗体标题栏 - 显示程序状态]
[开始监控] [配置▼] [新建] [删除] | [设区] [预览] [设置]
[技能列表 - 三列显示]
[状态栏]
```

### 特色功能
- **黄色粗体标题**: 金黄色标题栏实时显示程序状态
- **紧凑布局**: 优化的按钮排列，界面简洁高效
- **实时更新**: 配置、状态、按键记录实时显示

## 🔧 技术栈

- **GUI框架**: CustomTkinter 5.0+
- **图像处理**: OpenCV 4.5+ + PIL
- **按键模拟**: pynput 1.7+
- **配置管理**: JSON格式存储
- **多线程**: 后台监控支持

## 📋 更新日志

### v2.0.0 (2024-12-19) - 黄金标题版
- ✨ 添加黄色粗体标题栏
- 🎨 优化界面布局和按钮排列
- ⚡ 提升UI性能和响应速度
- 🔧 完善配置管理功能
- 🐛 修复多项界面和功能问题

[查看完整更新日志](VERSION.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来帮助改进项目！

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本工具仅供学习和研究使用，请遵守游戏相关规定和服务条款。使用本工具产生的任何后果由用户自行承担。

---

**🎮 祝您游戏愉快！**

如果这个项目对您有帮助，请给个 ⭐ Star 支持一下！
