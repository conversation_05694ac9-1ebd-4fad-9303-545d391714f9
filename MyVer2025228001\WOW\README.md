# WOW Project

这是一个WOW相关的自动化项目。

## 项目结构

- `XXD.py` - 主程序文件
- `skill_processor.py` - 技能处理模块
- `configs/` - 配置文件目录，包含各种角色和技能的配置
- `templates/` - 模板图片目录，用于图像识别
- `requirements.txt` - Python依赖包列表

## 配置文件

项目包含多个角色配置：
- BDK001, BFS1, FZ001 等各种角色配置
- 每个配置文件包含特定的技能设置和参数

## 模板文件

`templates/` 目录包含用于图像识别的模板图片，按角色和技能分类。

## 使用方法

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行主程序：
   ```bash
   python XXD.py
   ```

## 注意事项

请确保在使用前正确配置相关参数，并遵守游戏相关规定。
