#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复模板文件名编码问题
解决中文配置名称导致的模板文件名乱码问题
"""

import os
import shutil
import re
import json
from pathlib import Path

def analyze_template_files():
    """分析模板文件名编码问题"""
    print("=== 分析模板文件名编码问题 ===")
    
    template_dir = Path("templates")
    if not template_dir.exists():
        print("❌ templates目录不存在")
        return
    
    files = list(template_dir.glob("*.png"))
    print(f"找到 {len(files)} 个模板文件")
    
    # 分类文件
    normal_files = []
    corrupted_files = []
    
    for file in files:
        filename = file.name
        try:
            # 尝试用ASCII编码，如果失败说明包含非ASCII字符
            filename.encode('ascii')
            normal_files.append(file)
        except UnicodeEncodeError:
            # 检查是否是乱码
            if is_corrupted_encoding(filename):
                corrupted_files.append(file)
            else:
                normal_files.append(file)  # 正常的中文文件名
    
    print(f"\n文件分类:")
    print(f"正常文件: {len(normal_files)} 个")
    print(f"乱码文件: {len(corrupted_files)} 个")
    
    if corrupted_files:
        print(f"\n🔍 发现乱码文件:")
        for file in corrupted_files:
            print(f"  ❌ {file.name}")
            
            # 尝试解码
            original_name = try_decode_filename(file.name)
            if original_name:
                print(f"     可能的原始名称: {original_name}")
    
    return normal_files, corrupted_files

def is_corrupted_encoding(filename):
    """检查文件名是否是编码错误的结果"""
    # 检查是否包含常见的编码错误字符
    corrupted_patterns = [
        r'楦',  # 鸟的错误编码
        r'痉',  # 德的错误编码  
        r'涓',  # 一的错误编码
        r'鍙',  # 号的错误编码
    ]
    
    for pattern in corrupted_patterns:
        if re.search(pattern, filename):
            return True
    return False

def try_decode_filename(corrupted_name):
    """尝试解码乱码文件名"""
    try:
        # 移除文件扩展名
        name_part = corrupted_name.replace('_S-', '_S-').split('_S-')[0]
        
        # 常见的编码错误映射
        decode_map = {
            '楦熷痉涓€鍙': '鸟德一号',
            '楦熷痉': '鸟德',
            '涓€鍙': '一号',
        }
        
        for corrupted, original in decode_map.items():
            if corrupted in name_part:
                return original
        
        # 尝试其他解码方法
        try:
            # 尝试从UTF-8错误编码恢复
            bytes_data = name_part.encode('latin1')
            decoded = bytes_data.decode('utf-8')
            return decoded
        except:
            pass
            
        return None
    except Exception as e:
        print(f"解码失败: {e}")
        return None

def fix_template_files():
    """修复模板文件名编码问题"""
    print("\n=== 修复模板文件名编码问题 ===")
    
    normal_files, corrupted_files = analyze_template_files()
    
    if not corrupted_files:
        print("✅ 没有发现乱码文件，无需修复")
        return True
    
    # 创建备份目录
    backup_dir = Path("templates_backup")
    backup_dir.mkdir(exist_ok=True)
    
    fixed_count = 0
    failed_count = 0
    
    for corrupted_file in corrupted_files:
        try:
            print(f"\n🔧 修复文件: {corrupted_file.name}")
            
            # 备份原文件
            backup_path = backup_dir / corrupted_file.name
            shutil.copy2(corrupted_file, backup_path)
            print(f"   ✅ 已备份到: {backup_path}")
            
            # 尝试解码文件名
            original_name = try_decode_filename(corrupted_file.name)
            
            if original_name:
                # 重构正确的文件名
                # 提取技能编号部分
                skill_part = corrupted_file.name.split('_S-')[1] if '_S-' in corrupted_file.name else "1.png"
                new_filename = f"{original_name}_S-{skill_part}"
                new_path = corrupted_file.parent / new_filename
                
                print(f"   🔄 重命名为: {new_filename}")
                
                # 检查目标文件是否已存在
                if new_path.exists():
                    print(f"   ⚠️  目标文件已存在，跳过: {new_filename}")
                    continue
                
                # 重命名文件
                corrupted_file.rename(new_path)
                print(f"   ✅ 修复成功")
                fixed_count += 1
            else:
                print(f"   ❌ 无法解码文件名，建议手动处理")
                failed_count += 1
                
        except Exception as e:
            print(f"   ❌ 修复失败: {e}")
            failed_count += 1
    
    print(f"\n=== 修复结果 ===")
    print(f"成功修复: {fixed_count} 个文件")
    print(f"修复失败: {failed_count} 个文件")
    print(f"备份位置: {backup_dir}")
    
    return failed_count == 0

def update_config_template_paths():
    """更新配置文件中的模板路径"""
    print("\n=== 更新配置文件中的模板路径 ===")
    
    configs_dir = Path("configs")
    if not configs_dir.exists():
        print("❌ configs目录不存在")
        return False
    
    # 查找包含中文的配置文件
    chinese_configs = []
    for config_file in configs_dir.glob("*.json"):
        if config_file.name == "last_config.json":
            continue
            
        config_name = config_file.stem
        # 检查是否包含中文
        if re.search(r'[\u4e00-\u9fff]', config_name):
            chinese_configs.append(config_file)
    
    print(f"找到 {len(chinese_configs)} 个中文配置文件")
    
    updated_count = 0
    for config_file in chinese_configs:
        try:
            print(f"\n🔧 更新配置: {config_file.name}")
            
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 检查是否有技能绑定需要更新
            icon_bindings = config_data.get('icon_bindings', {})
            if not icon_bindings:
                print(f"   ℹ️  配置文件无技能绑定，跳过")
                continue
            
            # 更新模板路径
            updated = False
            for skill_name, skill_data in icon_bindings.items():
                old_template_path = skill_data.get('template_path', '')
                if old_template_path:
                    # 检查模板文件是否存在
                    if not os.path.exists(old_template_path):
                        # 尝试找到正确的模板文件
                        config_name = config_file.stem
                        new_template_path = f"templates/{config_name}_{skill_name}.png"
                        
                        if os.path.exists(new_template_path):
                            skill_data['template_path'] = new_template_path
                            updated = True
                            print(f"   ✅ 更新模板路径: {skill_name}")
                        else:
                            print(f"   ⚠️  找不到模板文件: {skill_name}")
            
            if updated:
                # 保存更新后的配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                print(f"   ✅ 配置文件已更新")
                updated_count += 1
            else:
                print(f"   ℹ️  配置文件无需更新")
                
        except Exception as e:
            print(f"   ❌ 更新配置失败: {e}")
    
    print(f"\n更新了 {updated_count} 个配置文件")
    return True

def clean_corrupted_files():
    """清理无法修复的乱码文件"""
    print("\n=== 清理无法修复的乱码文件 ===")
    
    template_dir = Path("templates")
    corrupted_files = []
    
    for file in template_dir.glob("*.png"):
        if is_corrupted_encoding(file.name):
            corrupted_files.append(file)
    
    if not corrupted_files:
        print("✅ 没有发现需要清理的乱码文件")
        return True
    
    print(f"发现 {len(corrupted_files)} 个乱码文件:")
    for file in corrupted_files:
        print(f"  🗑️  {file.name}")
    
    # 询问是否删除
    response = input("\n是否删除这些乱码文件? (y/N): ").strip().lower()
    if response == 'y':
        deleted_count = 0
        for file in corrupted_files:
            try:
                file.unlink()
                print(f"✅ 已删除: {file.name}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ 删除失败: {file.name} - {e}")
        
        print(f"\n删除了 {deleted_count} 个乱码文件")
        return True
    else:
        print("跳过删除操作")
        return False

def main():
    """主函数"""
    print("模板文件名编码问题修复工具")
    print("=" * 50)
    print("🎯 目标: 修复中文配置名称导致的模板文件名乱码问题")
    print("=" * 50)
    
    # 1. 分析问题
    normal_files, corrupted_files = analyze_template_files()
    
    if not corrupted_files:
        print("\n✅ 没有发现编码问题，模板文件名正常")
        return
    
    # 2. 修复文件名
    print(f"\n🔧 开始修复 {len(corrupted_files)} 个乱码文件...")
    fix_success = fix_template_files()
    
    # 3. 更新配置文件
    update_config_template_paths()
    
    # 4. 清理剩余的乱码文件
    clean_corrupted_files()
    
    print("\n" + "=" * 50)
    if fix_success:
        print("🎉 模板文件名编码问题修复完成！")
        print("\n✨ 修复内容:")
        print("1. ✅ 修复了乱码的模板文件名")
        print("2. ✅ 更新了配置文件中的模板路径")
        print("3. ✅ 创建了原文件备份")
        
        print("\n🎮 现在中文配置应该可以:")
        print("- 正常加载技能绑定")
        print("- 正确显示技能图标")
        print("- 完整使用所有功能")
    else:
        print("⚠️  部分文件修复失败，可能需要手动处理")
    
    print(f"\n💡 建议:")
    print("1. 重新测试中文配置的加载功能")
    print("2. 检查技能图标是否正常显示")
    print("3. 如有问题，可从备份目录恢复文件")

if __name__ == "__main__":
    main()
