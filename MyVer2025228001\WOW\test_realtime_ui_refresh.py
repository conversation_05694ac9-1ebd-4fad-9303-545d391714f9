#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时UI刷新测试脚本
用于验证实时添加技能时UI是否正确刷新
"""

import os
import sys
import json
import time
import numpy as np
import cv2

def test_ui_refresh_mechanism():
    """测试UI刷新机制"""
    print("=== 实时UI刷新测试 ===")
    
    try:
        # 导入主程序模块
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 初始化成功")
        
        # 测试配置加载
        config_dir = "configs"
        config_files = [f[:-5] for f in os.listdir(config_dir) if f.endswith('.json')]
        
        if not config_files:
            print("❌ 没有找到配置文件")
            return False
        
        # 选择一个配置进行测试
        test_spec = config_files[0]
        print(f"📝 使用配置进行测试: {test_spec}")
        
        if processor.load_config(test_spec):
            initial_count = len(processor.icon_bindings)
            print(f"   - 初始技能数量: {initial_count}")
            
            # 模拟添加新技能
            print("\n🔄 模拟添加新技能...")
            
            # 创建一个模拟的技能图标模板
            template = np.random.randint(0, 255, (48, 48, 3), dtype=np.uint8)
            
            # 生成新技能名称
            skill_count = len(processor.icon_bindings) + 1
            while f"test_skill_{skill_count}" in processor.icon_bindings:
                skill_count += 1
            new_skill_name = f"test_skill_{skill_count}"
            
            # 添加技能绑定
            binding = processor.add_icon_binding(
                name=new_skill_name,
                text=f"测试技能{skill_count}",
                hotkey="t",
                template_image=template
            )
            
            if binding:
                print(f"✅ 成功添加技能: {binding.text} -> {binding.hotkey}")
                
                # 检查技能是否添加到内存
                after_count = len(processor.icon_bindings)
                if after_count == initial_count + 1:
                    print(f"✅ 内存中技能数量正确: {initial_count} -> {after_count}")
                else:
                    print(f"❌ 内存中技能数量错误: {initial_count} -> {after_count}")
                    return False
                
                # 测试保存配置
                if processor.save_config(test_spec):
                    print(f"✅ 配置保存成功")
                    
                    # 重新加载配置验证持久化
                    processor.load_config(test_spec)
                    final_count = len(processor.icon_bindings)
                    if final_count == after_count:
                        print(f"✅ 技能添加已持久化: {final_count} 个技能")
                    else:
                        print(f"❌ 技能添加未持久化: 期望 {after_count}, 实际 {final_count}")
                        return False
                else:
                    print(f"❌ 配置保存失败")
                    return False
            else:
                print(f"❌ 添加技能失败")
                return False
        else:
            print(f"❌ 加载配置失败: {test_spec}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_mark_ui_dirty_functionality():
    """测试mark_ui_dirty功能"""
    print("\n=== mark_ui_dirty功能测试 ===")
    
    try:
        # 检查XXD.py中的mark_ui_dirty方法
        print("📝 检查mark_ui_dirty方法...")
        
        # 读取XXD.py文件内容
        with open("XXD.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键方法是否存在
        checks = [
            ("mark_ui_dirty方法", "def mark_ui_dirty(self):"),
            ("handle_new_icon中的mark_ui_dirty调用", "self.mark_ui_dirty()"),
            ("update_binding_list中的脏标记检查", "if not self._ui_dirty"),
            ("_ui_dirty属性初始化", "self._ui_dirty = False")
        ]
        
        all_passed = True
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✅ {check_name} 存在")
            else:
                print(f"❌ {check_name} 不存在")
                all_passed = False
        
        # 检查handle_new_icon方法中的修复
        if "self.mark_ui_dirty()" in content and "已标记UI为脏，强制更新技能列表" in content:
            print("✅ handle_new_icon方法已包含UI强制更新")
        else:
            print("❌ handle_new_icon方法缺少UI强制更新")
            all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查mark_ui_dirty功能时出错: {str(e)}")
        return False

def test_ui_update_flow():
    """测试UI更新流程"""
    print("\n=== UI更新流程测试 ===")
    
    try:
        # 模拟UI更新流程
        print("📝 模拟UI更新流程...")
        
        # 检查关键的更新点
        update_points = [
            "实时添加技能 (handle_new_icon)",
            "快速添加技能 (add_skill_binding)",
            "配置切换 (change_spec)",
            "技能删除 (remove_binding)",
            "创建新配置 (create_new_spec)"
        ]
        
        print("✅ 以下位置应该包含mark_ui_dirty()调用:")
        for point in update_points:
            print(f"   - {point}")
        
        # 读取文件检查
        with open("XXD.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计mark_ui_dirty调用次数
        mark_ui_dirty_count = content.count("self.mark_ui_dirty()")
        print(f"\n📊 mark_ui_dirty()调用次数: {mark_ui_dirty_count}")
        
        if mark_ui_dirty_count >= 5:  # 至少应该有5个调用点
            print("✅ mark_ui_dirty()调用次数充足")
            return True
        else:
            print("⚠️  mark_ui_dirty()调用次数可能不足")
            return False
        
    except Exception as e:
        print(f"❌ 测试UI更新流程时出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("WOW技能助手 - 实时UI刷新测试")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("skill_processor.py"):
        print("❌ 请在WOW项目目录中运行此测试")
        return
    
    # 运行测试
    test1_result = test_ui_refresh_mechanism()
    test2_result = test_mark_ui_dirty_functionality()
    test3_result = test_ui_update_flow()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"UI刷新机制测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"mark_ui_dirty功能测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"UI更新流程测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result and test2_result and test3_result:
        print("\n🎉 所有测试通过！实时UI刷新功能应该正常工作。")
        print("\n💡 使用说明:")
        print("1. 开启监控后，当检测到新技能图标时")
        print("2. 按下要绑定的按键")
        print("3. 技能将被添加并立即显示在技能列表中")
        print("4. 无需手动刷新，UI会自动更新")
    else:
        print("\n⚠️  部分测试失败，实时UI刷新可能存在问题。")
        
        if not test1_result:
            print("- 技能添加机制需要检查")
        if not test2_result:
            print("- mark_ui_dirty方法需要完善")
        if not test3_result:
            print("- UI更新调用点需要增加")

if __name__ == "__main__":
    main()
