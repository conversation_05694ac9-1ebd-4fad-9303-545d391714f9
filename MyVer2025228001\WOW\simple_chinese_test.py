#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的中文配置测试
"""

import os
import sys

def test_chinese_config():
    """简单测试中文配置"""
    print("=== 简单中文配置测试 ===")
    
    try:
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 创建成功")
        
        # 测试加载中文配置
        config_name = "鸟德一号"
        print(f"测试加载配置: {config_name}")
        
        result = processor.load_config(config_name)
        print(f"加载结果: {result}")
        
        if result:
            print("✅ 中文配置加载成功")
            print(f"监控区域: {processor.monitor_region}")
            print(f"技能绑定数量: {len(processor.icon_bindings)}")
            return True
        else:
            print("❌ 中文配置加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_files():
    """测试模板文件"""
    print("\n=== 模板文件测试 ===")
    
    templates_dir = "templates"
    if not os.path.exists(templates_dir):
        print("❌ templates目录不存在")
        return False
    
    # 查找中文模板文件
    chinese_templates = []
    for file in os.listdir(templates_dir):
        if "鸟德" in file:
            chinese_templates.append(file)
    
    print(f"找到 {len(chinese_templates)} 个鸟德相关模板文件:")
    for template in chinese_templates:
        print(f"  ✅ {template}")
    
    return len(chinese_templates) > 0

def main():
    """主函数"""
    print("中文配置简单测试")
    print("=" * 30)
    
    test1 = test_chinese_config()
    test2 = test_template_files()
    
    print("\n" + "=" * 30)
    print("测试结果:")
    print(f"中文配置加载: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"模板文件检查: {'✅ 通过' if test2 else '❌ 失败'}")
    
    if test1 and test2:
        print("\n🎉 中文配置问题已解决！")
    else:
        print("\n⚠️  仍有问题需要解决")

if __name__ == "__main__":
    main()
