#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制重建UI测试脚本
专门测试自动添加技能时是否真正使用完全重建而非增量更新
"""

import os
import sys
import json
import time
import numpy as np
import cv2

def test_force_rebuild_mechanism():
    """测试强制重建机制"""
    print("=== 强制重建机制测试 ===")
    
    try:
        # 检查代码中的强制重建逻辑
        print("📝 检查强制重建代码...")
        
        # 读取XXD.py文件内容
        with open("XXD.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键的强制重建机制
        rebuild_checks = [
            ("强制重建标志初始化", "_force_full_rebuild = False"),
            ("强制重建标志设置", "self._force_full_rebuild = True"),
            ("强制重建检查", "force_full_rebuild = getattr(self, '_force_full_rebuild', False)"),
            ("跳过增量更新", "跳过增量更新"),
            ("强制完全重建日志", "强制完全重建UI，跳过增量更新"),
            ("设置重建标志日志", "设置强制完全重建标志"),
            ("自动添加强制重建", "自动添加技能，强制完全重建UI"),
            ("快速添加强制重建", "快速添加技能，强制完全重建UI")
        ]
        
        all_passed = True
        for check_name, pattern in rebuild_checks:
            if pattern in content:
                print(f"✅ {check_name} 已实现")
            else:
                print(f"❌ {check_name} 未找到")
                all_passed = False
        
        # 检查增量更新的条件判断
        if "elif (not force_immediate and" in content:
            print("✅ 增量更新条件已修改为elif，确保强制重建优先")
        else:
            print("❌ 增量更新条件未正确修改")
            all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查强制重建机制时出错: {str(e)}")
        return False

def test_ui_update_flow():
    """测试UI更新流程"""
    print("\n=== UI更新流程测试 ===")
    
    try:
        # 导入主程序模块
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 初始化成功")
        
        # 测试配置加载
        config_dir = "configs"
        config_files = [f[:-5] for f in os.listdir(config_dir) if f.endswith('.json')]
        
        if not config_files:
            print("❌ 没有找到配置文件")
            return False
        
        # 选择一个配置进行测试
        test_spec = config_files[0]
        print(f"📝 使用配置进行测试: {test_spec}")
        
        if processor.load_config(test_spec):
            initial_count = len(processor.icon_bindings)
            print(f"   - 初始技能数量: {initial_count}")
            
            # 模拟强制重建的完整流程
            print("\n🔄 模拟强制重建流程...")
            
            # 创建模拟的技能图标模板
            template = np.random.randint(0, 255, (48, 48, 3), dtype=np.uint8)
            
            # 生成新技能名称
            skill_count = len(processor.icon_bindings) + 1
            while f"rebuild_test_{skill_count}" in processor.icon_bindings:
                skill_count += 1
            new_skill_name = f"rebuild_test_{skill_count}"
            
            print(f"📝 准备添加技能: {new_skill_name}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 1. 添加技能绑定
            binding = processor.add_icon_binding(
                name=new_skill_name,
                text=f"重建测试{skill_count}",
                hotkey="r",
                template_image=template
            )
            
            if binding:
                print(f"✅ 技能绑定创建成功: {binding.text}")
                
                # 2. 保存配置
                if processor.save_config(test_spec):
                    print(f"✅ 配置保存成功")
                    
                    # 3. 验证技能数量
                    current_count = len(processor.icon_bindings)
                    if current_count == initial_count + 1:
                        print(f"✅ 技能数量正确: {initial_count} -> {current_count}")
                    else:
                        print(f"❌ 技能数量错误: 期望 {initial_count + 1}, 实际 {current_count}")
                        return False
                else:
                    print(f"❌ 配置保存失败")
                    return False
            else:
                print(f"❌ 技能绑定创建失败")
                return False
            
            end_time = time.time()
            duration = (end_time - start_time) * 1000
            
            print(f"📊 强制重建流程总耗时: {duration:.1f}ms")
            
            # 性能评估
            if duration < 100:
                print(f"🎉 强制重建性能优秀: {duration:.1f}ms < 100ms")
                return True
            elif duration < 200:
                print(f"✅ 强制重建性能良好: {duration:.1f}ms < 200ms")
                return True
            else:
                print(f"⚠️  强制重建性能一般: {duration:.1f}ms >= 200ms")
                return False
        else:
            print(f"❌ 加载配置失败: {test_spec}")
            return False
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_incremental_vs_rebuild():
    """测试增量更新vs完全重建的区别"""
    print("\n=== 增量更新vs完全重建对比测试 ===")
    
    try:
        # 检查代码逻辑
        print("📝 分析UI更新逻辑...")
        
        with open("XXD.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找关键的逻辑分支
        lines = content.split('\n')
        
        # 找到update_binding_list方法
        in_update_method = False
        logic_flow = []
        
        for i, line in enumerate(lines):
            if "def update_binding_list(self, force_immediate=False):" in line:
                in_update_method = True
                continue
            
            if in_update_method:
                if line.strip().startswith("def ") and "update_binding_list" not in line:
                    break
                
                # 记录关键逻辑
                if any(keyword in line for keyword in [
                    "force_full_rebuild", 
                    "快速增量更新", 
                    "强制完全重建",
                    "跳过增量更新",
                    "elif (not force_immediate"
                ]):
                    logic_flow.append(f"第{i+1}行: {line.strip()}")
        
        print("🔍 关键逻辑流程:")
        for logic in logic_flow:
            print(f"   {logic}")
        
        # 验证逻辑正确性
        expected_patterns = [
            "force_full_rebuild = getattr",
            "强制完全重建UI，跳过增量更新",
            "elif (not force_immediate",
            "使用快速增量更新"
        ]
        
        found_patterns = 0
        for pattern in expected_patterns:
            if any(pattern in logic for logic in logic_flow):
                found_patterns += 1
                print(f"✅ 找到预期逻辑: {pattern}")
            else:
                print(f"❌ 缺少预期逻辑: {pattern}")
        
        if found_patterns == len(expected_patterns):
            print("✅ UI更新逻辑完整，强制重建优先于增量更新")
            return True
        else:
            print(f"⚠️  UI更新逻辑不完整: {found_patterns}/{len(expected_patterns)}")
            return False
        
    except Exception as e:
        print(f"❌ 分析UI更新逻辑时出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("WOW技能助手 - 强制重建UI测试")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("skill_processor.py"):
        print("❌ 请在WOW项目目录中运行此测试")
        return
    
    # 运行测试
    test1_result = test_force_rebuild_mechanism()
    test2_result = test_ui_update_flow()
    test3_result = test_incremental_vs_rebuild()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"强制重建机制测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"UI更新流程测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"增量vs重建对比测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result and test2_result and test3_result:
        print("\n🎉 所有测试通过！强制重建机制工作正常。")
        print("\n💡 修复效果:")
        print("1. 自动添加技能时强制使用完全重建，不再使用增量更新")
        print("2. 技能图标会一次性全部显示，不会一个一个慢慢出现")
        print("3. UI更新逻辑优先级正确：强制重建 > 增量更新")
        print("4. 性能保持优秀，用户体验大幅提升")
        
        print("\n🔧 技术实现:")
        print("- 添加_force_full_rebuild标志控制重建模式")
        print("- 修改update_binding_list逻辑，强制重建优先")
        print("- 自动添加和快速添加都使用force_full_rebuild=True")
        print("- 确保UI一次性完整更新，避免分批显示")
    else:
        print("\n⚠️  部分测试失败，强制重建机制需要进一步完善。")
        
        if not test1_result:
            print("- 强制重建机制代码需要检查")
        if not test2_result:
            print("- UI更新流程需要优化")
        if not test3_result:
            print("- 增量更新vs重建逻辑需要修正")

if __name__ == "__main__":
    main()
