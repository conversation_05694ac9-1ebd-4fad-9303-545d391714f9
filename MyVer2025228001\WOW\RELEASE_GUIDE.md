# 🚀 GitHub发布指南

## 📋 发布步骤

### 方法1: 使用自动化脚本 (推荐)

1. **运行发布脚本**
   ```bash
   cd WOW
   create_release.bat
   ```

2. **脚本会自动执行**
   - ✅ 检查Git状态
   - ✅ 添加所有文件到暂存区
   - ✅ 提交更改
   - ✅ 创建版本标签 v2.0.0
   - ✅ 推送到远程仓库
   - ✅ 推送标签

### 方法2: 手动操作

1. **提交所有更改**
   ```bash
   git add .
   git commit -m "🎉 Release v2.0.0 - 黄金标题版"
   ```

2. **创建标签**
   ```bash
   git tag -a v2.0.0 -m "WOW技能助手 v2.0.0 - 黄金标题版"
   ```

3. **推送到GitHub**
   ```bash
   git push origin main
   git push origin v2.0.0
   ```

## 🏷️ 在GitHub上创建正式发布

### 1. 访问GitHub仓库
- 打开: https://github.com/kookoo2024/My-WOW

### 2. 创建Release
1. 点击右侧的 **"Releases"**
2. 点击 **"Create a new release"**
3. 选择标签: **v2.0.0**
4. 填写发布标题: **🎮 WOW技能助手 v2.0.0 - 黄金标题版**

### 3. 发布说明
复制以下内容到发布说明中:

```markdown
# 🎉 WOW技能助手 v2.0.0 发布

## 🌟 重大更新 - "黄金标题版"

### ✨ 全新特性
- **黄色粗体标题栏**: 金黄色标题实时显示程序状态
- **优化界面布局**: 按钮重新排列，界面更紧凑合理
- **性能提升**: UI响应更流畅，图像处理更高效
- **功能完善**: 完善配置管理，增强程序稳定性

### 🎮 主要功能
- 🎯 智能技能识别和自动释放
- 🔧 多配置管理支持
- 📊 实时状态显示
- ⌨️ 完整的快捷键支持

### 🚀 快速开始
1. 下载并解压文件
2. 安装Python 3.8+
3. 运行: `pip install -r requirements.txt`
4. 启动: `python XXD.py`

### 📋 系统要求
- Windows 10 (1903+) / Windows 11
- Python 3.8+
- 4GB+ RAM

**🎮 祝您游戏愉快！**
```

### 4. 上传文件
在 **"Attach binaries"** 区域上传以下文件:
- `XXD.py` - 主程序
- `skill_processor.py` - 核心模块  
- `requirements.txt` - 依赖列表
- `README.md` - 项目说明
- `VERSION.md` - 版本信息

### 5. 发布设置
- ✅ **Set as the latest release** (设为最新版本)
- ❌ **Set as a pre-release** (不勾选预发布)

### 6. 点击发布
点击 **"Publish release"** 完成发布

## 🔄 自动化发布 (GitHub Actions)

项目已配置GitHub Actions工作流，当推送标签时会自动:
- ✅ 测试应用程序
- ✅ 创建GitHub Release
- ✅ 上传相关文件
- ✅ 生成发布说明

## 📝 发布后检查清单

### ✅ 验证发布
- [ ] GitHub Release页面显示正常
- [ ] 所有文件都已上传
- [ ] 发布说明格式正确
- [ ] 标签版本号正确

### ✅ 通知用户
- [ ] 更新项目README
- [ ] 在相关社区分享更新
- [ ] 回复用户反馈和问题

### ✅ 后续工作
- [ ] 监控用户反馈
- [ ] 收集改进建议
- [ ] 规划下一版本功能

## 🎯 版本号规则

### 格式: vX.Y.Z
- **X (主版本)**: 重大功能更新或架构变更
- **Y (次版本)**: 新功能添加或重要改进
- **Z (修订版)**: 问题修复和小改进

### 示例
- `v2.0.0` - 黄金标题版 (重大界面更新)
- `v2.1.0` - 智能优化版 (新功能添加)
- `v2.1.1` - 问题修复版 (Bug修复)

## 📞 支持

如果发布过程中遇到问题:
1. 检查Git状态和权限
2. 确认GitHub仓库访问权限
3. 验证标签格式是否正确
4. 查看GitHub Actions日志

**发布成功后，您的WOW技能助手就可以让更多用户使用了！** 🎉
