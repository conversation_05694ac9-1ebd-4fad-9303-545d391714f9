#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文配置名称问题最终验证脚本
"""

import os
import sys
import json
import traceback
from skill_processor import HekiliProcessor

def test_chinese_config_complete():
    """完整测试中文配置名称功能"""
    print("=== 中文配置名称完整功能测试 ===")
    
    # 创建处理器
    processor = HekiliProcessor()
    
    # 测试用的中文配置名称
    test_configs = [
        "鸟德一号",
        "测试中文配置",
        "冰霜法师配置",
        "火焰法师PVP",
        "奶德团本配置"
    ]
    
    success_count = 0
    
    for config_name in test_configs:
        print(f"\n--- 测试配置: {config_name} ---")
        
        try:
            # 1. 测试保存中文配置
            processor.monitor_region = (100, 100, 50, 50)
            processor.settings = {
                "monitor_hotkey": "`",
                "threshold": 0.9,
                "scan_interval": 0.33,
                "key_press_delay": 0.19,
                "auto_add_skills": True
            }
            
            save_result = processor.save_config(config_name)
            print(f"保存结果: {'✅ 成功' if save_result else '❌ 失败'}")
            
            if not save_result:
                continue
            
            # 2. 测试加载中文配置
            processor.clear_bindings()  # 清空绑定
            load_result = processor.load_config(config_name)
            print(f"加载结果: {'✅ 成功' if load_result else '❌ 失败'}")
            
            if not load_result:
                continue
            
            # 3. 验证配置内容
            if processor.monitor_region == [100, 100, 50, 50]:
                print("✅ 监控区域正确")
            else:
                print(f"❌ 监控区域错误: {processor.monitor_region}")
                continue
            
            if processor.settings.get("threshold") == 0.9:
                print("✅ 设置正确")
            else:
                print(f"❌ 设置错误: {processor.settings}")
                continue
            
            # 4. 测试配置文件是否存在
            config_path = os.path.join("configs", f"{config_name}.json")
            if os.path.exists(config_path):
                print("✅ 配置文件存在")
            else:
                print(f"❌ 配置文件不存在: {config_path}")
                continue
            
            # 5. 测试文件内容可读性
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                if "monitor_region" in config_data and "settings" in config_data:
                    print("✅ 配置文件内容正确")
                else:
                    print("❌ 配置文件内容错误")
                    continue
            
            success_count += 1
            print(f"✅ 配置 '{config_name}' 测试通过")
            
        except Exception as e:
            print(f"❌ 配置 '{config_name}' 测试失败: {str(e)}")
            traceback.print_exc()
    
    print(f"\n=== 测试结果 ===")
    print(f"成功: {success_count}/{len(test_configs)}")
    print(f"成功率: {success_count/len(test_configs)*100:.1f}%")
    
    return success_count == len(test_configs)

def test_display_name_function():
    """测试显示名称函数"""
    print("\n=== 测试显示名称函数 ===")
    
    # 模拟XXD.py中的get_display_name方法
    def get_display_name(spec_name, max_length=6):
        """
        获取配置名称的显示版本
        对于中文名称，优先显示完整名称，如果太长则智能截取
        """
        if not spec_name:
            return ""
        
        # 如果名称长度在合理范围内，直接返回
        if len(spec_name) <= max_length:
            return spec_name
        
        # 检查是否包含中文字符
        import re
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', spec_name))
        
        if has_chinese:
            # 对于中文名称，取前4个字符加省略号
            if len(spec_name) > 4:
                return spec_name[:4] + "..."
            else:
                return spec_name
        else:
            # 对于英文名称，保持原有逻辑
            return spec_name[-max_length:]
    
    test_cases = [
        ("BFS1", "BFS1"),
        ("HKPVEANMU001", "MU001"),
        ("鸟德一号", "鸟德一号"),
        ("测试中文配置", "测试中文..."),
        ("很长的中文配置名称测试", "很长的中..."),
        ("VeryLongEnglishConfigName", "gName"),
        ("短名", "短名"),
        ("A", "A"),
        ("", "")
    ]
    
    all_passed = True
    for input_name, expected in test_cases:
        result = get_display_name(input_name)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_name}' -> '{result}' (期望: '{expected}')")
        if result != expected:
            all_passed = False
    
    return all_passed

def test_config_list_with_chinese():
    """测试包含中文的配置列表加载"""
    print("\n=== 测试配置列表加载（包含中文） ===")
    
    try:
        config_dir = "configs"
        if not os.path.exists(config_dir):
            print(f"❌ 配置目录不存在: {config_dir}")
            return False
        
        # 获取所有配置文件
        files = os.listdir(config_dir)
        json_files = [f for f in files if f.endswith('.json') and f != 'last_config.json']
        
        print(f"找到 {len(json_files)} 个配置文件:")
        
        chinese_configs = []
        english_configs = []
        
        for file in json_files:
            spec_name = file[:-5]  # 移除.json后缀
            
            # 检查是否包含中文
            import re
            has_chinese = bool(re.search(r'[\u4e00-\u9fff]', spec_name))
            
            if has_chinese:
                chinese_configs.append(spec_name)
                print(f"  🈶 {spec_name} (中文配置)")
            else:
                english_configs.append(spec_name)
                print(f"  🔤 {spec_name} (英文配置)")
        
        print(f"\n统计:")
        print(f"中文配置: {len(chinese_configs)} 个")
        print(f"英文配置: {len(english_configs)} 个")
        
        # 测试加载每个中文配置
        processor = HekiliProcessor()
        failed_configs = []
        
        for config_name in chinese_configs:
            try:
                result = processor.load_config(config_name)
                if result:
                    print(f"✅ 中文配置加载成功: {config_name}")
                else:
                    print(f"❌ 中文配置加载失败: {config_name}")
                    failed_configs.append(config_name)
            except Exception as e:
                print(f"❌ 中文配置加载异常: {config_name} - {str(e)}")
                failed_configs.append(config_name)
        
        if failed_configs:
            print(f"\n❌ 失败的配置: {failed_configs}")
            return False
        else:
            print(f"\n✅ 所有中文配置加载成功")
            return True
        
    except Exception as e:
        print(f"❌ 测试配置列表时出错: {str(e)}")
        return False

def cleanup_test_configs():
    """清理测试配置文件"""
    print("\n=== 清理测试配置文件 ===")
    
    test_configs = [
        "测试中文配置",
        "冰霜法师配置", 
        "火焰法师PVP",
        "奶德团本配置"
    ]
    
    for config_name in test_configs:
        try:
            config_path = os.path.join("configs", f"{config_name}.json")
            if os.path.exists(config_path):
                os.remove(config_path)
                print(f"✅ 已删除测试配置: {config_name}")
        except Exception as e:
            print(f"❌ 删除测试配置失败: {config_name} - {str(e)}")

def main():
    """主测试函数"""
    print("中文配置名称问题最终验证")
    print("=" * 60)
    print("🎯 验证目标:")
    print("1. 中文配置名称可以正常保存和加载")
    print("2. 配置文件内容正确")
    print("3. 显示名称函数工作正常")
    print("4. 不会出现加载错误")
    print("=" * 60)
    
    # 运行所有测试
    test1_result = test_chinese_config_complete()
    test2_result = test_display_name_function()
    test3_result = test_config_list_with_chinese()
    
    print("\n" + "=" * 60)
    print("最终验证结果:")
    print(f"中文配置完整功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"显示名称函数测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"配置列表加载测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result and test2_result and test3_result:
        print("\n🎉 所有测试通过！中文配置名称问题已彻底解决！")
        print("\n✨ 解决方案总结:")
        print("1. ✅ 修复了load_config返回值逻辑")
        print("2. ✅ 添加了智能显示名称函数")
        print("3. ✅ 修复了所有UI显示问题")
        print("4. ✅ 确保UTF-8编码正确处理")
        
        print("\n🎮 现在您可以:")
        print("- 创建中文名称的配置文件")
        print("- 正常加载和切换中文配置")
        print("- 在UI中看到正确的中文显示")
        print("- 不会再出现加载错误")
        
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")
    
    # 清理测试文件
    cleanup_test_configs()
    
    print(f"\n🏆 问题解决状态: {'已彻底解决 ✅' if all([test1_result, test2_result, test3_result]) else '需要进一步修复 ⚠️'}")

if __name__ == "__main__":
    main()
