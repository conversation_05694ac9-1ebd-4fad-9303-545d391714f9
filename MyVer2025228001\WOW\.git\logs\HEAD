0000000000000000000000000000000000000000 ecb180547851abbae8919b097bd1820ff38b4a07 Lao9CH <<EMAIL>> 1750220150 +0800	commit (initial): Initial commit: WOW project
ecb180547851abbae8919b097bd1820ff38b4a07 a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 Lao9CH <<EMAIL>> 1750220196 +0800	commit: Add README.md with project documentation
a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 Lao9CH <<EMAIL>> 1750220875 +0800	checkout: moving from master to a9a2bdc
a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 Lao9CH <<EMAIL>> 1750220889 +0800	checkout: moving from a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 to master
a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 Lao9CH <<EMAIL>> 1750220896 +0800	checkout: moving from master to stable-backup
a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 Lao9CH <<EMAIL>> 1750220916 +0800	reset: moving to a9a2bdc
a9a2bdc0bfaa0ff47d20873f35d19d9ab8dc2aa1 a12581f125d069f6512e3bcb31a48c87ac73992d Lao9CH <<EMAIL>> 1750221310 +0800	commit: 性能优化：UI更新频率控制、图像哈希缓存、内存管理优化
a12581f125d069f6512e3bcb31a48c87ac73992d 9a4a9ef18b05c52378938995f93da6f7b0dc1acd Lao9CH <<EMAIL>> 1750221618 +0800	commit: 修复配置切换时技能图标不显示的问题
9a4a9ef18b05c52378938995f93da6f7b0dc1acd 2853d8a678525d64d9774a587ac78b49d23d7f8c Lao9CH <<EMAIL>> 1750221700 +0800	commit: 添加配置切换测试脚本，验证修复效果
2853d8a678525d64d9774a587ac78b49d23d7f8c cf38a16be1bb75f46be7ed8a7c43b51bf0c55888 Lao9CH <<EMAIL>> 1750222014 +0800	commit: 修复技能删除功能
