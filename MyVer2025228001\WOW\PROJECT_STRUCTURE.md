# WOW技能助手 - 项目结构

## 📁 目录结构

```
WOW/
├── XXD.py                    # 主程序文件
├── skill_processor.py        # 技能处理核心模块
├── requirements.txt          # Python依赖包列表
├── README.md                # 项目说明文档
├── PROJECT_STRUCTURE.md     # 项目结构说明（本文件）
├── configs/                 # 配置文件目录
│   ├── last_config.json    # 最后使用的配置
│   ├── BFS1.json           # 配置文件示例
│   ├── shejiLR01.json      # 配置文件示例
│   └── 鸟德一号.json        # 配置文件示例
└── templates/               # 技能图标模板目录
    ├── BFS1_S-*.png        # BFS1配置的技能图标
    ├── shejiLR01_S-*.png   # shejiLR01配置的技能图标
    └── 鸟德一号_S-*.png     # 鸟德一号配置的技能图标
```

## 🚀 启动程序

```bash
cd WOW
python XXD.py
```

## 📋 核心文件说明

### XXD.py
- **作用**: 主程序入口，包含GUI界面和主要逻辑
- **功能**: 
  - 创建用户界面
  - 处理用户交互
  - 管理配置切换
  - 监控技能释放

### skill_processor.py
- **作用**: 技能处理核心模块
- **功能**:
  - 图像识别和匹配
  - 技能绑定管理
  - 配置文件读写
  - 按键模拟

### configs/
- **作用**: 存储所有配置文件
- **格式**: JSON格式
- **内容**: 技能绑定、监控区域、设置参数等

### templates/
- **作用**: 存储技能图标模板
- **格式**: PNG图像文件
- **命名**: [配置名]_[技能名].png

## 🎮 主要功能

1. **技能监控**: 实时监控游戏技能建议区域
2. **自动释放**: 根据识别结果自动按键
3. **配置管理**: 支持多套配置切换
4. **图标识别**: 基于模板匹配的图标识别
5. **黄色粗体标题**: 醒目的程序状态显示

## 🔧 技术特点

- **GUI框架**: CustomTkinter
- **图像处理**: OpenCV + PIL
- **按键模拟**: pynput
- **配置存储**: JSON格式
- **多线程**: 支持后台监控

## 📝 注意事项

- 确保安装了所有依赖包：`pip install -r requirements.txt`
- 首次使用需要创建配置和设置监控区域
- 技能图标模板会自动保存到templates目录
- 配置文件支持中文名称但建议使用英文避免编码问题

**项目已清理完毕，结构简洁明了！** 🎉
