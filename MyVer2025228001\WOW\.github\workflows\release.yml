name: 🚀 Release WOW技能助手

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    name: 📦 Create Release
    runs-on: windows-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: 📦 Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🧪 Test application
      run: |
        python -c "import XXD; print('✅ Application imports successfully')"
        
    - name: 📋 Extract version
      id: version
      run: |
        $version = $env:GITHUB_REF -replace 'refs/tags/', ''
        echo "VERSION=$version" >> $env:GITHUB_OUTPUT
        echo "Version: $version"
        
    - name: 📝 Create Release Notes
      id: release_notes
      run: |
        $version = "${{ steps.version.outputs.VERSION }}"
        $notes = @"
        # 🎉 WOW技能助手 $version 发布
        
        ## 🌟 主要特性
        - 🎮 智能技能识别和自动释放
        - 🎨 黄色粗体标题栏，程序状态一目了然
        - 🔧 多配置管理支持
        - ⚡ 优化的UI性能和响应速度
        
        ## 🚀 快速开始
        1. 下载并解压文件
        2. 安装Python 3.8+
        3. 运行: ``pip install -r requirements.txt``
        4. 启动: ``python XXD.py``
        
        ## 📋 系统要求
        - Windows 10 (1903+) / Windows 11
        - Python 3.8+
        - 4GB+ RAM
        
        ## 🎯 使用指南
        1. 创建配置 → 设置监控区域 → 添加技能 → 开始监控
        2. 快捷键: `` ` `` 开始/停止, F9 自动添加, F10 添加技能
        
        **🎮 祝您游戏愉快！**
        "@
        
        $notes | Out-File -FilePath release_notes.md -Encoding UTF8
        echo "RELEASE_NOTES=release_notes.md" >> $env:GITHUB_OUTPUT
        
    - name: 🏷️ Create Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.version.outputs.VERSION }}
        name: 🎮 WOW技能助手 ${{ steps.version.outputs.VERSION }}
        body_path: release_notes.md
        draft: false
        prerelease: false
        files: |
          XXD.py
          skill_processor.py
          requirements.txt
          README.md
          VERSION.md
          RELEASE_NOTES.md
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 🎉 Success Notification
      run: |
        echo "🎉 Release ${{ steps.version.outputs.VERSION }} created successfully!"
        echo "📦 Files uploaded to GitHub Releases"
        echo "🔗 Release URL: https://github.com/${{ github.repository }}/releases/tag/${{ steps.version.outputs.VERSION }}"
