# 配置结构改进方案

## 🎯 **设计目标**

1. **层次化结构** - 清晰的配置层次
2. **语义化命名** - 有意义的技能名称
3. **元数据支持** - 配置版本、描述等
4. **模块化设计** - 分离不同类型的配置
5. **向后兼容** - 支持旧配置迁移

## 📁 **新的目录结构**

```
configs/
├── profiles/           # 配置文件目录
│   ├── BFS1/
│   │   ├── profile.json      # 主配置文件
│   │   ├── skills.json       # 技能配置
│   │   ├── settings.json     # 个性化设置
│   │   └── templates/        # 技能模板图片
│   │       ├── frost_bolt.png
│   │       ├── ice_lance.png
│   │       └── ...
│   └── HKPVEANMU001/
│       ├── profile.json
│       ├── skills.json
│       ├── settings.json
│       └── templates/
├── global/             # 全局配置
│   ├── settings.json         # 全局默认设置
│   └── keybinds.json        # 全局按键绑定
└── schemas/            # 配置文件模式
    ├── profile.schema.json
    ├── skills.schema.json
    └── settings.schema.json
```

## 📄 **配置文件结构**

### 1. **profile.json** - 主配置文件

```json
{
  "metadata": {
    "name": "冰霜法师配置",
    "description": "适用于团本的冰霜法师技能配置",
    "class": "法师",
    "spec": "冰霜",
    "version": "1.2.0",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T15:45:00Z",
    "author": "玩家名称",
    "tags": ["团本", "高伤害", "AOE"]
  },
  "monitor": {
    "region": {
      "x": 1192,
      "y": 768,
      "width": 50,
      "height": 50
    },
    "enabled": true,
    "auto_detect": true
  },
  "ui": {
    "theme": "dark",
    "layout": "grid",
    "icon_size": 32,
    "show_stats": true
  }
}
```

### 2. **skills.json** - 技能配置

```json
{
  "skills": {
    "frost_bolt": {
      "name": "寒冰箭",
      "category": "基础技能",
      "hotkey": "1",
      "priority": 1,
      "cooldown": 0.5,
      "template": "frost_bolt.png",
      "threshold": 0.9,
      "enabled": true,
      "stats": {
        "usage_count": 156,
        "avg_similarity": 0.95,
        "last_used": "2024-01-20T15:30:00Z"
      }
    },
    "ice_lance": {
      "name": "冰枪术",
      "category": "爆发技能",
      "hotkey": "2",
      "priority": 2,
      "cooldown": 0.3,
      "template": "ice_lance.png",
      "threshold": 0.88,
      "enabled": true,
      "combo": ["frost_bolt"],
      "conditions": {
        "requires_buff": "手指冰冷",
        "min_mana": 100
      }
    },
    "blizzard": {
      "name": "暴风雪",
      "category": "AOE技能",
      "hotkey": "shift+3",
      "priority": 3,
      "cooldown": 1.5,
      "template": "blizzard.png",
      "threshold": 0.85,
      "enabled": true,
      "area_effect": true,
      "min_targets": 3
    }
  },
  "categories": {
    "基础技能": {
      "color": "#4CAF50",
      "description": "常用的基础攻击技能"
    },
    "爆发技能": {
      "color": "#FF5722",
      "description": "高伤害爆发技能"
    },
    "AOE技能": {
      "color": "#2196F3",
      "description": "群体攻击技能"
    }
  }
}
```

### 3. **settings.json** - 个性化设置

```json
{
  "detection": {
    "scan_interval": 0.33,
    "threshold": 0.9,
    "auto_add_skills": true,
    "detection_method": "hash_comparison"
  },
  "input": {
    "key_press_delay": 0.19,
    "monitor_hotkey": "`",
    "enable_combos": true,
    "smart_casting": true
  },
  "performance": {
    "max_fps": 30,
    "cache_templates": true,
    "parallel_detection": false,
    "memory_limit": "512MB"
  },
  "notifications": {
    "sound_enabled": true,
    "visual_feedback": true,
    "log_level": "info"
  }
}
```

### 4. **global/settings.json** - 全局默认设置

```json
{
  "defaults": {
    "detection": {
      "scan_interval": 0.5,
      "threshold": 0.85,
      "auto_add_skills": false
    },
    "input": {
      "key_press_delay": 0.2,
      "monitor_hotkey": "`"
    }
  },
  "ui": {
    "language": "zh-CN",
    "theme": "auto",
    "startup_profile": "last_used"
  },
  "advanced": {
    "debug_mode": false,
    "backup_configs": true,
    "auto_update": true
  }
}
```

## 🔧 **技术改进**

### 1. **配置验证**
- JSON Schema验证
- 类型检查
- 必填字段验证
- 值范围验证

### 2. **版本管理**
- 配置文件版本控制
- 自动迁移机制
- 向后兼容性
- 变更日志

### 3. **模板管理**
- 独立的模板目录
- 模板版本控制
- 自动去重
- 压缩存储

### 4. **配置继承**
- 全局默认设置
- 配置文件继承
- 设置覆盖机制
- 环境特定配置

## 🎨 **用户体验改进**

### 1. **可视化编辑**
- 图形化配置界面
- 拖拽式技能排序
- 实时预览
- 批量操作

### 2. **智能建议**
- 技能优先级建议
- 按键冲突检测
- 性能优化建议
- 配置模板推荐

### 3. **导入导出**
- 配置文件导出
- 云端同步
- 配置分享
- 批量导入

## 📊 **数据分析**

### 1. **使用统计**
- 技能使用频率
- 匹配准确率
- 性能指标
- 错误日志

### 2. **优化建议**
- 阈值自动调整
- 热键使用分析
- 配置优化建议
- 性能瓶颈识别

## 🔄 **迁移方案**

### 1. **自动迁移**
```python
def migrate_old_config(old_config_path):
    """自动迁移旧配置到新结构"""
    # 读取旧配置
    # 转换为新格式
    # 保存到新位置
    # 备份旧配置
```

### 2. **兼容性层**
```python
class ConfigAdapter:
    """配置适配器，支持新旧格式"""
    def load_config(self, path):
        # 自动检测配置格式
        # 适配到统一接口
```

## 💡 **实施建议**

### 阶段1: 基础重构
1. 设计新的配置结构
2. 实现配置读写类
3. 添加迁移工具
4. 保持向后兼容

### 阶段2: 功能增强
1. 添加配置验证
2. 实现版本管理
3. 优化模板管理
4. 增加统计功能

### 阶段3: 用户体验
1. 图形化配置界面
2. 智能建议系统
3. 云端同步功能
4. 社区分享平台

## 🎯 **预期收益**

1. **更好的组织性** - 清晰的文件结构
2. **更强的扩展性** - 模块化设计
3. **更好的维护性** - 版本控制和验证
4. **更佳的用户体验** - 直观的配置管理
5. **更高的可靠性** - 错误检测和恢复
