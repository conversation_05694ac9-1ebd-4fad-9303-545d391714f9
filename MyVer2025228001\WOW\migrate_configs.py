#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置迁移工具
将旧的扁平化配置迁移到新的层次化结构
"""

import os
import json
from pathlib import Path
from config_manager_v2 import ConfigManagerV2, ProfileMetadata

def migrate_all_configs():
    """迁移所有旧配置到新格式"""
    print("=== 配置迁移工具 ===")
    
    # 创建新的配置管理器
    config_manager = ConfigManagerV2()
    
    # 查找所有旧配置文件
    old_configs_dir = Path("configs")
    old_config_files = list(old_configs_dir.glob("*.json"))
    
    # 排除特殊文件
    exclude_files = {"last_config.json"}
    old_config_files = [f for f in old_config_files if f.name not in exclude_files]
    
    print(f"找到 {len(old_config_files)} 个旧配置文件")
    
    migrated_count = 0
    failed_count = 0
    
    for config_file in old_config_files:
        profile_id = config_file.stem  # 文件名不含扩展名
        print(f"\n正在迁移: {config_file.name} -> {profile_id}")
        
        try:
            # 加载旧配置
            with open(config_file, 'r', encoding='utf-8') as f:
                old_config = json.load(f)
            
            # 分析配置内容，生成合适的元数据
            metadata = analyze_old_config(profile_id, old_config)
            
            # 创建新配置文件
            if config_manager.create_profile(profile_id, metadata):
                # 迁移配置数据
                if migrate_config_data(config_manager, profile_id, old_config):
                    print(f"✅ 成功迁移: {profile_id}")
                    migrated_count += 1
                else:
                    print(f"❌ 数据迁移失败: {profile_id}")
                    failed_count += 1
            else:
                print(f"❌ 创建配置失败: {profile_id}")
                failed_count += 1
                
        except Exception as e:
            print(f"❌ 迁移失败 {profile_id}: {e}")
            failed_count += 1
    
    print(f"\n=== 迁移完成 ===")
    print(f"成功迁移: {migrated_count} 个")
    print(f"迁移失败: {failed_count} 个")
    print(f"总计: {len(old_config_files)} 个")
    
    # 显示新配置列表
    print(f"\n=== 新配置列表 ===")
    profiles = config_manager.list_profiles()
    for profile in profiles:
        print(f"📁 {profile['name']} ({profile['id']})")
        print(f"   描述: {profile['description']}")
        print(f"   职业: {profile['class']} - {profile['spec']}")
        print(f"   更新: {profile['updated_at'][:19]}")

def analyze_old_config(profile_id: str, old_config: dict) -> ProfileMetadata:
    """分析旧配置，生成合适的元数据"""
    
    # 根据配置ID推测职业和专精
    class_spec_mapping = {
        "BFS": ("法师", "冰霜"),
        "FIRE": ("法师", "火焰"),
        "ARCANE": ("法师", "奥术"),
        "BDK": ("死亡骑士", "鲜血"),
        "UDK": ("死亡骑士", "邪恶"),
        "FDK": ("死亡骑士", "冰霜"),
        "PROT": ("战士", "防护"),
        "FURY": ("战士", "狂怒"),
        "ARMS": ("战士", "武器"),
        "HOLY": ("圣骑士", "神圣"),
        "RET": ("圣骑士", "惩戒"),
        "PROT": ("圣骑士", "防护"),
    }
    
    # 尝试匹配职业专精
    class_name = "未知"
    spec_name = "未知"
    
    for key, (cls, spec) in class_spec_mapping.items():
        if key in profile_id.upper():
            class_name = cls
            spec_name = spec
            break
    
    # 生成描述
    skill_count = len(old_config.get("icon_bindings", {}))
    description = f"从旧配置迁移，包含 {skill_count} 个技能绑定"
    
    # 生成标签
    tags = []
    if "PVE" in profile_id.upper():
        tags.append("PVE")
    if "PVP" in profile_id.upper():
        tags.append("PVP")
    if "RAID" in profile_id.upper():
        tags.append("团本")
    if "M+" in profile_id.upper() or "MYTHIC" in profile_id.upper():
        tags.append("大秘境")
    
    if not tags:
        tags.append("通用")
    
    return ProfileMetadata(
        name=f"{class_name}{spec_name}配置",
        description=description,
        class_name=class_name,
        spec_name=spec_name,
        version="1.0.0",
        author="系统迁移",
        tags=tags
    )

def migrate_config_data(config_manager: ConfigManagerV2, profile_id: str, old_config: dict) -> bool:
    """迁移具体的配置数据"""
    try:
        # 加载新创建的配置
        new_config = config_manager.load_profile(profile_id)
        if not new_config:
            return False
        
        # 迁移监控区域
        if "monitor_region" in old_config:
            region = old_config["monitor_region"]
            if len(region) >= 4:
                new_config["monitor"]["region"] = {
                    "x": region[0],
                    "y": region[1],
                    "width": region[2],
                    "height": region[3]
                }
        
        # 迁移设置
        if "settings" in old_config:
            old_settings = old_config["settings"]
            
            # 检测设置
            detection_settings = new_config["settings"]["detection"]
            detection_settings.update({
                "scan_interval": old_settings.get("scan_interval", 0.33),
                "threshold": old_settings.get("threshold", 0.9),
                "auto_add_skills": old_settings.get("auto_add_skills", True)
            })
            
            # 输入设置
            input_settings = new_config["settings"]["input"]
            input_settings.update({
                "key_press_delay": old_settings.get("key_press_delay", 0.19),
                "monitor_hotkey": old_settings.get("monitor_hotkey", "`")
            })
        
        # 迁移技能绑定
        if "icon_bindings" in old_config:
            for skill_id, skill_data in old_config["icon_bindings"].items():
                # 确定技能分类
                category = categorize_skill(skill_data.get("text", skill_id), skill_data["hotkey"])
                
                # 创建新的技能配置
                skill_config = {
                    "name": skill_data.get("text", skill_id),
                    "category": category,
                    "hotkey": skill_data["hotkey"],
                    "priority": 1,
                    "cooldown": 0.5,
                    "template": f"{skill_id}.png",
                    "threshold": 0.9,
                    "enabled": True,
                    "description": f"从 {skill_id} 迁移",
                    "combo": [],
                    "conditions": {},
                    "stats": {
                        "usage_count": 0,
                        "avg_similarity": 0.0,
                        "last_used": ""
                    }
                }
                
                new_config["skills"][skill_id] = skill_config
        
        # 保存迁移后的配置
        return config_manager.save_profile(profile_id, new_config)
        
    except Exception as e:
        print(f"迁移配置数据失败: {e}")
        return False

def categorize_skill(skill_name: str, hotkey: str) -> str:
    """根据技能名称和热键推测技能分类"""
    
    # 基于技能名称的分类
    name_lower = skill_name.lower()
    
    # 治疗技能
    healing_keywords = ["治疗", "恢复", "回血", "生命", "heal"]
    if any(keyword in name_lower for keyword in healing_keywords):
        return "辅助技能"
    
    # 爆发技能
    burst_keywords = ["爆发", "爆炸", "暴击", "终结", "大招", "burst", "explosion"]
    if any(keyword in name_lower for keyword in burst_keywords):
        return "爆发技能"
    
    # 防御技能
    defense_keywords = ["护盾", "防御", "格挡", "减伤", "shield", "defense"]
    if any(keyword in name_lower for keyword in defense_keywords):
        return "辅助技能"
    
    # 基于热键的分类
    # 功能键通常是重要技能
    if hotkey.startswith("f") or "alt+" in hotkey or "ctrl+" in hotkey:
        return "爆发技能"
    
    # 数字键通常是基础技能
    if hotkey.isdigit():
        return "基础技能"
    
    # 默认分类
    return "基础技能"

def compare_configs():
    """对比新旧配置结构"""
    print("\n=== 配置结构对比 ===")
    
    print("📊 旧配置结构:")
    print("  ├── 单一JSON文件")
    print("  ├── monitor_region (监控区域)")
    print("  ├── settings (设置)")
    print("  └── icon_bindings (技能绑定)")
    print("      ├── hotkey")
    print("      ├── template_path")
    print("      └── text")
    
    print("\n📊 新配置结构:")
    print("  ├── profiles/{profile_id}/")
    print("  │   ├── profile.json (主配置)")
    print("  │   │   ├── metadata (元数据)")
    print("  │   │   ├── monitor (监控配置)")
    print("  │   │   └── ui (界面配置)")
    print("  │   ├── skills.json (技能配置)")
    print("  │   │   ├── skills (技能列表)")
    print("  │   │   └── categories (分类定义)")
    print("  │   ├── settings.json (设置)")
    print("  │   │   ├── detection (检测设置)")
    print("  │   │   ├── input (输入设置)")
    print("  │   │   ├── performance (性能设置)")
    print("  │   │   └── notifications (通知设置)")
    print("  │   └── templates/ (模板目录)")
    print("  ├── global/ (全局配置)")
    print("  └── schemas/ (配置模式)")
    
    print("\n✨ 改进点:")
    print("  ✅ 层次化结构，更好的组织")
    print("  ✅ 元数据支持，便于管理")
    print("  ✅ 技能分类，更直观的展示")
    print("  ✅ 模块化设置，更灵活的配置")
    print("  ✅ 独立模板目录，更好的文件管理")
    print("  ✅ 全局默认设置，减少重复配置")

if __name__ == "__main__":
    # 显示配置结构对比
    compare_configs()
    
    # 执行迁移
    migrate_all_configs()
    
    print(f"\n🎉 迁移完成！新配置位于 configs/profiles/ 目录下")
    print(f"💡 旧配置文件仍然保留，您可以安全地测试新配置")
    print(f"🔧 如需要，可以修改 config_manager_v2.py 来适配您的具体需求")
