#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复中文配置名称显示问题的脚本
"""

import os
import re

def get_display_name(spec_name, max_length=6):
    """
    获取配置名称的显示版本
    对于中文名称，优先显示完整名称，如果太长则智能截取
    """
    if not spec_name:
        return ""
    
    # 如果名称长度在合理范围内，直接返回
    if len(spec_name) <= max_length:
        return spec_name
    
    # 检查是否包含中文字符
    has_chinese = bool(re.search(r'[\u4e00-\u9fff]', spec_name))
    
    if has_chinese:
        # 对于中文名称，取前4个字符加省略号
        if len(spec_name) > 4:
            return spec_name[:4] + "..."
        else:
            return spec_name
    else:
        # 对于英文名称，保持原有逻辑
        return spec_name[-max_length:]

def fix_xxd_display_names():
    """修复XXD.py中的配置名称显示问题"""
    print("=== 修复XXD.py中的配置名称显示 ===")
    
    xxd_file = "XXD.py"
    if not os.path.exists(xxd_file):
        print(f"❌ 文件不存在: {xxd_file}")
        return False
    
    try:
        # 读取文件内容
        with open(xxd_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_file = xxd_file + '.backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已创建备份文件: {backup_file}")
        
        # 定义替换模式
        replacements = [
            # 窗口标题中的配置名称显示
            (r'self\.root\.title\(f"孟子 - \{([^}]+)\[-6:\]\}"\)', 
             r'self.root.title(f"孟子 - {self.get_display_name(\1)}")'),
            
            # 状态标签中的配置名称显示
            (r'self\.status_label\.configure\(text=f"([^"]*)\{([^}]+)\[-6:\]\}([^"]*)"\)',
             r'self.status_label.configure(text=f"\1{self.get_display_name(\2)}\3")'),
            
            # 确认对话框中的配置名称显示
            (r'f"确定要删除 \{([^}]+)\[-6:\]\} 的配置吗',
             r'f"确定要删除 {self.get_display_name(\1)} 的配置吗'),
        ]
        
        # 执行替换
        modified = False
        for pattern, replacement in replacements:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                content = new_content
                modified = True
                print(f"✅ 应用替换: {pattern[:50]}...")
        
        if modified:
            # 在类中添加get_display_name方法
            class_pattern = r'(class WoWSkillAssistant:.*?def __init__\(self\):)'
            method_code = '''
    def get_display_name(self, spec_name, max_length=6):
        """
        获取配置名称的显示版本
        对于中文名称，优先显示完整名称，如果太长则智能截取
        """
        if not spec_name:
            return ""
        
        # 如果名称长度在合理范围内，直接返回
        if len(spec_name) <= max_length:
            return spec_name
        
        # 检查是否包含中文字符
        import re
        has_chinese = bool(re.search(r'[\\u4e00-\\u9fff]', spec_name))
        
        if has_chinese:
            # 对于中文名称，取前4个字符加省略号
            if len(spec_name) > 4:
                return spec_name[:4] + "..."
            else:
                return spec_name
        else:
            # 对于英文名称，保持原有逻辑
            return spec_name[-max_length:]

'''
            
            # 查找类定义位置并插入方法
            if 'def get_display_name(' not in content:
                # 在__init__方法之前插入新方法
                init_pattern = r'(\s+def __init__\(self\):)'
                content = re.sub(init_pattern, method_code + r'\1', content)
                print("✅ 已添加get_display_name方法")
            
            # 保存修改后的文件
            with open(xxd_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已修复文件: {xxd_file}")
            return True
        else:
            print("ℹ️  文件无需修改")
            return True
            
    except Exception as e:
        print(f"❌ 修复文件时出错: {str(e)}")
        return False

def test_display_names():
    """测试配置名称显示函数"""
    print("\n=== 测试配置名称显示 ===")
    
    test_cases = [
        "BFS1",
        "HKPVEANMU001", 
        "鸟德一号",
        "测试中文配置",
        "很长的中文配置名称测试",
        "VeryLongEnglishConfigName",
        "短名",
        "A"
    ]
    
    for name in test_cases:
        display_name = get_display_name(name)
        print(f"原名称: '{name}' -> 显示名称: '{display_name}'")

def main():
    """主函数"""
    print("中文配置名称显示修复工具")
    print("=" * 50)
    
    # 测试显示名称函数
    test_display_names()
    
    # 修复XXD.py文件
    fix_xxd_display_names()
    
    print("\n" + "=" * 50)
    print("修复完成")
    print("\n建议:")
    print("1. 现在中文配置名称应该可以正常加载了")
    print("2. 配置名称显示会更加友好")
    print("3. 如果有问题，可以从备份文件恢复")

if __name__ == "__main__":
    main()
