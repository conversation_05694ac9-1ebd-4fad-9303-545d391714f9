@echo off
echo ========================================
echo    WOW技能助手 v2.0.0 发布脚本
echo ========================================
echo.

echo 📋 准备创建版本标签和推送到GitHub...
echo.

echo 🔍 检查Git状态...
git status
echo.

echo 📝 添加所有文件到暂存区...
git add .
echo.

echo 💾 提交更改...
git commit -m "🎉 Release v2.0.0 - 黄金标题版

✨ 主要更新:
- 添加黄色粗体标题栏，程序状态一目了然
- 优化界面布局，按钮排列更合理
- 隐藏添加技能按钮，界面更紧凑
- 移除当前坐标显示，减少冗余信息
- 优化UI性能，响应更流畅
- 完善错误处理机制

🎮 功能特色:
- 智能技能识别和自动释放
- 多配置管理支持
- 实时状态显示
- 完整的快捷键支持

🔧 技术改进:
- 优化图像处理性能
- 改进内存管理
- 完善配置文件处理
- 增强程序稳定性

📦 项目结构清理:
- 移除所有测试文件
- 清理多余文档
- 保持核心功能文件
- 项目结构更清晰"
echo.

echo 🏷️ 创建版本标签...
git tag -a v2.0.0 -m "WOW技能助手 v2.0.0 - 黄金标题版

🌟 重大更新:
- 全新黄色粗体标题栏
- 优化界面布局设计
- 提升用户体验
- 性能优化改进

🎯 主要特性:
- 智能技能识别
- 自动技能释放
- 多配置管理
- 实时状态显示

📅 发布日期: 2024-12-19
🎮 适用于: World of Warcraft
💻 支持系统: Windows 10/11"
echo.

echo 📤 推送到远程仓库...
git push origin main
echo.

echo 🏷️ 推送标签...
git push origin v2.0.0
echo.

echo ✅ 发布完成！
echo.
echo 🎉 WOW技能助手 v2.0.0 已成功发布到GitHub！
echo.
echo 📋 后续步骤:
echo    1. 访问 GitHub 仓库
echo    2. 在 Releases 页面创建正式发布
echo    3. 上传发布说明和文件
echo    4. 通知用户更新
echo.
echo 🔗 GitHub 仓库: https://github.com/kookoo2024/My-WOW
echo.

pause
