#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文配置名称的加载问题
"""

import os
import sys
import json
import traceback
from skill_processor import HekiliProcessor

def test_chinese_config_loading():
    """测试中文配置名称的加载"""
    print("=== 测试中文配置名称加载 ===")
    
    # 创建处理器
    processor = HekiliProcessor()
    
    # 测试加载中文配置
    chinese_config_name = "鸟德一号"
    print(f"尝试加载中文配置: {chinese_config_name}")
    
    try:
        # 测试加载
        result = processor.load_config(chinese_config_name)
        print(f"加载结果: {result}")
        
        if result:
            print("✅ 中文配置加载成功")
            print(f"监控区域: {processor.monitor_region}")
            print(f"设置: {processor.settings}")
            print(f"技能绑定数量: {len(processor.icon_bindings)}")
        else:
            print("❌ 中文配置加载失败")
            
    except Exception as e:
        print(f"❌ 加载中文配置时出错: {str(e)}")
        print("详细错误信息:")
        traceback.print_exc()

def test_chinese_config_saving():
    """测试中文配置名称的保存"""
    print("\n=== 测试中文配置名称保存 ===")
    
    # 创建处理器
    processor = HekiliProcessor()
    
    # 设置一些测试数据
    processor.monitor_region = (100, 100, 50, 50)
    processor.settings = {
        "monitor_hotkey": "`",
        "threshold": 0.9,
        "scan_interval": 0.33,
        "key_press_delay": 0.19,
        "auto_add_skills": True
    }
    
    # 测试保存中文配置
    chinese_config_name = "测试中文配置"
    print(f"尝试保存中文配置: {chinese_config_name}")
    
    try:
        # 测试保存
        result = processor.save_config(chinese_config_name)
        print(f"保存结果: {result}")
        
        if result:
            print("✅ 中文配置保存成功")
            
            # 验证文件是否存在
            config_path = os.path.join("configs", f"{chinese_config_name}.json")
            if os.path.exists(config_path):
                print(f"✅ 配置文件已创建: {config_path}")
                
                # 尝试读取文件内容
                with open(config_path, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                    print(f"✅ 文件内容读取成功: {content}")
            else:
                print(f"❌ 配置文件未找到: {config_path}")
        else:
            print("❌ 中文配置保存失败")
            
    except Exception as e:
        print(f"❌ 保存中文配置时出错: {str(e)}")
        print("详细错误信息:")
        traceback.print_exc()

def test_file_system_encoding():
    """测试文件系统编码问题"""
    print("\n=== 测试文件系统编码 ===")
    
    try:
        # 检查系统编码
        print(f"系统默认编码: {sys.getdefaultencoding()}")
        print(f"文件系统编码: {sys.getfilesystemencoding()}")
        
        # 测试中文文件名操作
        chinese_filename = "测试中文文件名.txt"
        test_content = "这是测试内容"
        
        # 写入测试文件
        with open(chinese_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"✅ 成功创建中文文件名: {chinese_filename}")
        
        # 读取测试文件
        with open(chinese_filename, 'r', encoding='utf-8') as f:
            read_content = f.read()
        print(f"✅ 成功读取中文文件: {read_content}")
        
        # 清理测试文件
        os.remove(chinese_filename)
        print("✅ 测试文件已清理")
        
    except Exception as e:
        print(f"❌ 文件系统编码测试失败: {str(e)}")
        traceback.print_exc()

def test_config_list_loading():
    """测试配置列表加载"""
    print("\n=== 测试配置列表加载 ===")
    
    try:
        config_dir = "configs"
        if os.path.exists(config_dir):
            print(f"配置目录存在: {config_dir}")
            
            # 列出所有配置文件
            files = os.listdir(config_dir)
            json_files = [f for f in files if f.endswith('.json')]
            
            print(f"找到 {len(json_files)} 个JSON配置文件:")
            for file in json_files:
                print(f"  - {file}")
                
                # 检查是否包含中文
                try:
                    file.encode('ascii')
                    print(f"    ✅ ASCII文件名")
                except UnicodeEncodeError:
                    print(f"    🈶 包含非ASCII字符(可能是中文)")
                
                # 尝试加载每个配置文件
                try:
                    config_path = os.path.join(config_dir, file)
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    print(f"    ✅ 文件内容加载成功")
                except Exception as e:
                    print(f"    ❌ 文件内容加载失败: {str(e)}")
        else:
            print(f"❌ 配置目录不存在: {config_dir}")
            
    except Exception as e:
        print(f"❌ 配置列表加载测试失败: {str(e)}")
        traceback.print_exc()

def test_template_path_handling():
    """测试模板路径处理"""
    print("\n=== 测试模板路径处理 ===")
    
    try:
        # 测试中文配置名称的模板路径
        chinese_config = "鸟德一号"
        skill_name = "S-1"
        
        # 构建模板路径
        template_path = os.path.join("templates", f"{chinese_config}_{skill_name}.png")
        print(f"模板路径: {template_path}")
        
        # 检查路径是否存在
        if os.path.exists(template_path):
            print(f"✅ 模板文件存在")
        else:
            print(f"❌ 模板文件不存在")
            
            # 列出templates目录中的相关文件
            template_dir = "templates"
            if os.path.exists(template_dir):
                files = os.listdir(template_dir)
                matching_files = [f for f in files if chinese_config in f]
                print(f"找到 {len(matching_files)} 个相关模板文件:")
                for file in matching_files:
                    print(f"  - {file}")
            
    except Exception as e:
        print(f"❌ 模板路径处理测试失败: {str(e)}")
        traceback.print_exc()

def main():
    """主测试函数"""
    print("中文配置名称问题诊断工具")
    print("=" * 50)
    
    # 运行所有测试
    test_file_system_encoding()
    test_config_list_loading()
    test_chinese_config_loading()
    test_chinese_config_saving()
    test_template_path_handling()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
