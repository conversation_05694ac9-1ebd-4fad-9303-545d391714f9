#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置切换测试脚本
用于验证配置切换时技能图标是否正确加载
"""

import os
import sys
import json
import time

def test_config_loading():
    """测试配置加载功能"""
    print("=== 配置切换测试 ===")
    
    # 检查配置目录
    config_dir = "configs"
    if not os.path.exists(config_dir):
        print("❌ 配置目录不存在")
        return False
    
    # 获取所有配置文件
    config_files = [f for f in os.listdir(config_dir) if f.endswith('.json')]
    if not config_files:
        print("❌ 没有找到配置文件")
        return False
    
    print(f"✅ 找到 {len(config_files)} 个配置文件:")
    for config_file in config_files:
        print(f"   - {config_file}")
    
    # 测试每个配置文件
    for config_file in config_files:
        spec_name = config_file[:-5]  # 移除.json后缀
        print(f"\n--- 测试配置: {spec_name} ---")
        
        config_path = os.path.join(config_dir, config_file)
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查配置结构
            if 'icon_bindings' not in config:
                print(f"⚠️  配置 {spec_name} 没有技能绑定")
                continue
            
            bindings = config['icon_bindings']
            print(f"✅ 配置包含 {len(bindings)} 个技能绑定:")
            
            # 检查每个绑定的模板文件
            missing_templates = []
            for name, binding_data in bindings.items():
                template_path = os.path.join("templates", f"{spec_name}_{name}.png")
                if os.path.exists(template_path):
                    text = binding_data.get('text', name)
                    hotkey = binding_data.get('hotkey', '?')
                    print(f"   ✅ {text} -> {hotkey} (模板: {template_path})")
                else:
                    missing_templates.append(template_path)
                    print(f"   ❌ {name} -> 模板文件缺失: {template_path}")
            
            if missing_templates:
                print(f"⚠️  配置 {spec_name} 有 {len(missing_templates)} 个模板文件缺失")
            else:
                print(f"✅ 配置 {spec_name} 所有模板文件完整")
                
        except Exception as e:
            print(f"❌ 加载配置 {spec_name} 时出错: {str(e)}")
    
    return True

def test_ui_update_mechanism():
    """测试UI更新机制"""
    print("\n=== UI更新机制测试 ===")
    
    try:
        # 导入主程序模块
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 初始化成功")
        
        # 测试配置加载
        config_dir = "configs"
        config_files = [f[:-5] for f in os.listdir(config_dir) if f.endswith('.json')]
        
        if config_files:
            test_spec = config_files[0]
            print(f"📝 测试加载配置: {test_spec}")
            
            if processor.load_config(test_spec):
                print(f"✅ 配置 {test_spec} 加载成功")
                print(f"   - 技能绑定数量: {len(processor.icon_bindings)}")
                print(f"   - 监控区域: {processor.monitor_region}")
                
                # 列出所有技能
                for name, binding in processor.icon_bindings.items():
                    print(f"   - {binding.text} ({name}) -> {binding.hotkey}")
                
            else:
                print(f"❌ 配置 {test_spec} 加载失败")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("WOW技能助手 - 配置切换测试")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("skill_processor.py"):
        print("❌ 请在WOW项目目录中运行此测试")
        return
    
    # 运行测试
    test1_result = test_config_loading()
    test2_result = test_ui_update_mechanism()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"配置文件测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"UI更新测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！配置切换功能应该正常工作。")
    else:
        print("\n⚠️  部分测试失败，请检查配置文件和模板文件。")

if __name__ == "__main__":
    main()
