{"monitor_region": [1192, 768, 50, 50], "settings": {"monitor_hotkey": "`", "threshold": 0.9, "scan_interval": 0.33, "key_press_delay": 0.19, "auto_add_skills": false}, "icon_bindings": {"S-2": {"hotkey": "2", "template_path": "templates\\BFS1_S-2.png", "text": "S-2"}, "S-3": {"hotkey": "f5", "template_path": "templates\\BFS1_S-3.png", "text": "S-3"}, "S-4": {"hotkey": "f2", "template_path": "templates\\BFS1_S-4.png", "text": "S-4"}, "S-5": {"hotkey": ",", "template_path": "templates\\BFS1_S-5.png", "text": "S-5"}, "S-6": {"hotkey": "z", "template_path": "templates\\BFS1_S-6.png", "text": "S-6"}, "S-7": {"hotkey": "alt+3", "template_path": "templates\\BFS1_S-7.png", "text": "S-7"}, "S-8": {"hotkey": "f1", "template_path": "templates\\BFS1_S-8.png", "text": "S-8"}, "S-9": {"hotkey": "f7", "template_path": "templates\\BFS1_S-9.png", "text": "S-9"}, "S-10": {"hotkey": "f3", "template_path": "templates\\BFS1_S-10.png", "text": "S-10"}, "S-11": {"hotkey": "2", "template_path": "templates\\BFS1_S-11.png", "text": "S-11"}, "S-12": {"hotkey": "f7", "template_path": "templates\\BFS1_S-12.png", "text": "S-12"}, "S-13": {"hotkey": "2", "template_path": "templates\\BFS1_S-13.png", "text": "S-13"}, "S-14": {"hotkey": "8", "template_path": "templates\\BFS1_S-14.png", "text": "S-14"}, "S-15": {"hotkey": ";", "template_path": "templates\\BFS1_S-15.png", "text": "S-15"}, "S-16": {"hotkey": "f", "template_path": "templates\\BFS1_S-16.png", "text": "S-16"}, "S-17": {"hotkey": "w", "template_path": "templates\\BFS1_S-17.png", "text": "S-17"}, "S-18": {"hotkey": "t", "template_path": "templates\\BFS1_S-18.png", "text": "S-18"}, "S-19": {"hotkey": "e", "template_path": "templates\\BFS1_S-19.png", "text": "S-19"}, "S-20": {"hotkey": "w", "template_path": "templates\\BFS1_S-20.png", "text": "S-20"}, "S-21": {"hotkey": "esc", "template_path": "templates\\BFS1_S-21.png", "text": "S-21"}, "test_skill_21": {"hotkey": "t", "template_path": "templates\\BFS1_test_skill_21.png", "text": "测试技能21"}, "perf_test_22": {"hotkey": "1", "template_path": "templates\\BFS1_perf_test_22.png", "text": "性能测试22"}, "perf_test_23": {"hotkey": "2", "template_path": "templates\\BFS1_perf_test_23.png", "text": "性能测试23"}, "perf_test_24": {"hotkey": "3", "template_path": "templates\\BFS1_perf_test_24.png", "text": "性能测试24"}, "perf_test_25": {"hotkey": "4", "template_path": "templates\\BFS1_perf_test_25.png", "text": "性能测试25"}, "perf_test_26": {"hotkey": "5", "template_path": "templates\\BFS1_perf_test_26.png", "text": "性能测试26"}, "auto_test_27": {"hotkey": "1", "template_path": "templates\\BFS1_auto_test_27.png", "text": "自动测试27"}, "auto_test_28": {"hotkey": "2", "template_path": "templates\\BFS1_auto_test_28.png", "text": "自动测试28"}, "auto_test_29": {"hotkey": "3", "template_path": "templates\\BFS1_auto_test_29.png", "text": "自动测试29"}, "rebuild_test_30": {"hotkey": "r", "template_path": "templates\\BFS1_rebuild_test_30.png", "text": "重建测试30"}, "final_test_1": {"hotkey": "1", "template_path": "templates\\BFS1_final_test_1.png", "text": "最终测试1"}, "final_test_2": {"hotkey": "2", "template_path": "templates\\BFS1_final_test_2.png", "text": "最终测试2"}, "final_test_3": {"hotkey": "3", "template_path": "templates\\BFS1_final_test_3.png", "text": "最终测试3"}, "simple_test_1": {"hotkey": "s1", "template_path": "templates\\BFS1_simple_test_1.png", "text": "简单测试1"}, "simple_test_2": {"hotkey": "s2", "template_path": "templates\\BFS1_simple_test_2.png", "text": "简单测试2"}, "simple_test_3": {"hotkey": "s3", "template_path": "templates\\BFS1_simple_test_3.png", "text": "简单测试3"}, "simple_test_38": {"hotkey": "s1", "template_path": "templates\\BFS1_simple_test_38.png", "text": "简单测试38"}, "simple_test_39": {"hotkey": "s2", "template_path": "templates\\BFS1_simple_test_39.png", "text": "简单测试39"}, "simple_test_40": {"hotkey": "s3", "template_path": "templates\\BFS1_simple_test_40.png", "text": "简单测试40"}}}