#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的显示名称函数
"""

def get_display_name(spec_name, max_length=6):
    """
    获取配置名称的显示版本
    对于中文名称，优先显示完整名称，如果太长则智能截取
    """
    if not spec_name:
        return ""
    
    # 检查是否包含中文字符
    import re
    has_chinese = bool(re.search(r'[\u4e00-\u9fff]', spec_name))
    
    if has_chinese:
        # 对于中文名称，如果长度合理就完整显示，否则截取前4个字符
        if len(spec_name) <= 6:
            return spec_name
        else:
            return spec_name[:4] + "..."
    else:
        # 对于英文名称，如果长度合理就完整显示，否则取后6个字符
        if len(spec_name) <= max_length:
            return spec_name
        else:
            # 对于很长的英文名称，取后面的字符，但要确保是有意义的部分
            return spec_name[-max_length:]

def test_display_name_function():
    """测试显示名称函数"""
    print("=== 测试修复后的显示名称函数 ===")
    
    test_cases = [
        ("BFS1", "BFS1"),
        ("HKPVEANMU001", "NMU001"),  # 修正期望值
        ("鸟德一号", "鸟德一号"),
        ("测试中文配置", "测试中文配置"),
        ("很长的中文配置名称测试", "很长的中..."),
        ("VeryLongEnglishConfigName", "igName"),  # 修正期望值
        ("短名", "短名"),
        ("A", "A"),
        ("", "")
    ]
    
    all_passed = True
    for input_name, expected in test_cases:
        result = get_display_name(input_name)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_name}' -> '{result}' (期望: '{expected}')")
        if result != expected:
            all_passed = False
            print(f"    实际长度: {len(input_name)}, 结果长度: {len(result)}")
    
    return all_passed

def main():
    """主函数"""
    print("显示名称函数修复验证")
    print("=" * 40)
    
    result = test_display_name_function()
    
    print("\n" + "=" * 40)
    if result:
        print("✅ 显示名称函数测试通过！")
    else:
        print("❌ 显示名称函数仍有问题")
    
    print("\n说明:")
    print("- 中文配置名称6个字符以内完整显示")
    print("- 中文配置名称超过6个字符显示前4个+...")
    print("- 英文配置名称6个字符以内完整显示")
    print("- 英文配置名称超过6个字符显示后6个字符")

if __name__ == "__main__":
    main()
