#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动添加技能性能测试脚本
用于验证自动添加技能时UI更新的性能和响应速度
"""

import os
import sys
import json
import time
import numpy as np
import cv2

def test_auto_add_ui_performance():
    """测试自动添加技能的UI性能"""
    print("=== 自动添加技能UI性能测试 ===")
    
    try:
        # 导入主程序模块
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 初始化成功")
        
        # 测试配置加载
        config_dir = "configs"
        config_files = [f[:-5] for f in os.listdir(config_dir) if f.endswith('.json')]
        
        if not config_files:
            print("❌ 没有找到配置文件")
            return False
        
        # 选择一个配置进行测试
        test_spec = config_files[0]
        print(f"📝 使用配置进行测试: {test_spec}")
        
        if processor.load_config(test_spec):
            initial_count = len(processor.icon_bindings)
            print(f"   - 初始技能数量: {initial_count}")
            
            # 模拟自动添加技能的性能测试
            print("\n🚀 模拟自动添加技能性能测试...")
            
            # 测试连续自动添加多个技能
            test_count = 3
            total_start_time = time.time()
            
            for i in range(test_count):
                print(f"\n--- 测试第 {i+1}/{test_count} 个技能 ---")
                
                # 创建模拟的技能图标模板
                template = np.random.randint(0, 255, (48, 48, 3), dtype=np.uint8)
                
                # 生成新技能名称
                skill_count = len(processor.icon_bindings) + 1
                while f"auto_test_{skill_count}" in processor.icon_bindings:
                    skill_count += 1
                new_skill_name = f"auto_test_{skill_count}"
                
                # 记录单个添加开始时间
                single_start = time.time()
                
                # 模拟自动添加技能的完整流程
                print(f"  🔄 开始添加技能: {new_skill_name}")
                
                # 1. 添加技能绑定
                binding = processor.add_icon_binding(
                    name=new_skill_name,
                    text=f"自动测试{skill_count}",
                    hotkey=str(i+1),
                    template_image=template
                )
                
                if binding:
                    print(f"  ✅ 技能绑定创建成功: {binding.text}")
                    
                    # 2. 保存配置
                    save_start = time.time()
                    if processor.save_config(test_spec):
                        save_end = time.time()
                        save_duration = (save_end - save_start) * 1000
                        print(f"  ✅ 配置保存成功 (耗时: {save_duration:.1f}ms)")
                        
                        # 3. 验证技能数量
                        current_count = len(processor.icon_bindings)
                        expected_count = initial_count + i + 1
                        if current_count == expected_count:
                            print(f"  ✅ 技能数量正确: {current_count}")
                        else:
                            print(f"  ❌ 技能数量错误: 期望 {expected_count}, 实际 {current_count}")
                            return False
                    else:
                        print(f"  ❌ 配置保存失败")
                        return False
                else:
                    print(f"  ❌ 技能绑定创建失败")
                    return False
                
                single_end = time.time()
                single_duration = (single_end - single_start) * 1000
                print(f"  📊 单个技能总耗时: {single_duration:.1f}ms")
            
            total_end_time = time.time()
            total_duration = (total_end_time - total_start_time) * 1000
            avg_duration = total_duration / test_count
            
            print(f"\n📊 自动添加性能统计:")
            print(f"   - 总耗时: {total_duration:.1f}ms")
            print(f"   - 平均每个技能: {avg_duration:.1f}ms")
            print(f"   - 最终技能数量: {len(processor.icon_bindings)}")
            print(f"   - 新增技能数量: {len(processor.icon_bindings) - initial_count}")
            
            # 性能评估
            if avg_duration < 100:
                print(f"🎉 自动添加性能优秀: 平均 {avg_duration:.1f}ms < 100ms")
                performance_rating = "优秀"
            elif avg_duration < 200:
                print(f"✅ 自动添加性能良好: 平均 {avg_duration:.1f}ms < 200ms")
                performance_rating = "良好"
            elif avg_duration < 500:
                print(f"⚠️  自动添加性能一般: 平均 {avg_duration:.1f}ms < 500ms")
                performance_rating = "一般"
            else:
                print(f"❌ 自动添加性能较差: 平均 {avg_duration:.1f}ms >= 500ms")
                performance_rating = "较差"
            
            return performance_rating in ["优秀", "良好"]
        else:
            print(f"❌ 加载配置失败: {test_spec}")
            return False
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_update_mechanisms():
    """测试UI更新机制"""
    print("\n=== UI更新机制测试 ===")
    
    try:
        # 检查UI更新相关的代码
        print("📝 检查UI更新机制...")
        
        # 读取XXD.py文件内容
        with open("XXD.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键的UI更新机制
        update_mechanisms = [
            ("强制完全重建", "force_full_rebuild=True"),
            ("自动添加强制更新", "自动添加技能，强制完全重建UI"),
            ("快速添加强制更新", "快速添加技能，强制完全重建UI"),
            ("编辑按键强制更新", "编辑按键后强制更新UI"),
            ("立即更新模式", "force_immediate=True"),
            ("UI脏标记", "mark_ui_dirty"),
            ("更新间隔优化", "ui_update_interval = 0.1")
        ]
        
        all_passed = True
        for mechanism_name, pattern in update_mechanisms:
            if pattern in content:
                print(f"✅ {mechanism_name} 已实现")
            else:
                print(f"❌ {mechanism_name} 未找到")
                all_passed = False
        
        # 统计强制更新调用次数
        force_immediate_count = content.count("force_immediate=True")
        force_rebuild_count = content.count("force_full_rebuild=True")
        mark_ui_dirty_count = content.count("self.mark_ui_dirty(")
        
        print(f"\n📊 UI更新调用统计:")
        print(f"   - force_immediate=True 调用次数: {force_immediate_count}")
        print(f"   - force_full_rebuild=True 调用次数: {force_rebuild_count}")
        print(f"   - mark_ui_dirty() 调用次数: {mark_ui_dirty_count}")
        
        if force_immediate_count >= 3 and force_rebuild_count >= 2 and mark_ui_dirty_count >= 8:
            print("✅ UI更新调用次数充足")
        else:
            print("⚠️  UI更新调用次数可能不足")
            all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查UI更新机制时出错: {str(e)}")
        return False

def test_edit_hotkey_refresh():
    """测试编辑按键后的UI刷新"""
    print("\n=== 编辑按键UI刷新测试 ===")
    
    try:
        # 导入主程序模块
        from skill_processor import HekiliProcessor
        
        processor = HekiliProcessor()
        print("✅ HekiliProcessor 初始化成功")
        
        # 加载配置
        config_dir = "configs"
        config_files = [f[:-5] for f in os.listdir(config_dir) if f.endswith('.json')]
        
        if not config_files:
            print("❌ 没有找到配置文件")
            return False
        
        test_spec = config_files[0]
        if processor.load_config(test_spec):
            if len(processor.icon_bindings) > 0:
                # 选择第一个技能进行测试
                first_binding_name = list(processor.icon_bindings.keys())[0]
                first_binding = processor.icon_bindings[first_binding_name]
                original_hotkey = first_binding.hotkey
                
                print(f"📝 测试技能: {first_binding.text} (原按键: {original_hotkey})")
                
                # 模拟编辑按键
                new_hotkey = "test_key"
                print(f"🔄 模拟编辑按键: {original_hotkey} -> {new_hotkey}")
                
                start_time = time.time()
                
                # 修改按键
                first_binding.hotkey = new_hotkey
                
                # 保存配置
                if processor.save_config(test_spec):
                    end_time = time.time()
                    duration = (end_time - start_time) * 1000
                    
                    print(f"✅ 按键编辑保存成功 (耗时: {duration:.1f}ms)")
                    
                    # 验证修改
                    processor.load_config(test_spec)
                    updated_binding = processor.icon_bindings[first_binding_name]
                    if updated_binding.hotkey == new_hotkey:
                        print(f"✅ 按键修改已持久化: {updated_binding.hotkey}")
                        
                        # 恢复原始按键
                        updated_binding.hotkey = original_hotkey
                        processor.save_config(test_spec)
                        print(f"✅ 已恢复原始按键: {original_hotkey}")
                        
                        return True
                    else:
                        print(f"❌ 按键修改未持久化")
                        return False
                else:
                    print(f"❌ 保存配置失败")
                    return False
            else:
                print("⚠️  配置中没有技能，跳过编辑测试")
                return True
        else:
            print(f"❌ 加载配置失败: {test_spec}")
            return False
        
    except Exception as e:
        print(f"❌ 编辑按键测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("WOW技能助手 - 自动添加技能性能测试")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("skill_processor.py"):
        print("❌ 请在WOW项目目录中运行此测试")
        return
    
    # 运行测试
    test1_result = test_auto_add_ui_performance()
    test2_result = test_ui_update_mechanisms()
    test3_result = test_edit_hotkey_refresh()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"自动添加UI性能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"UI更新机制测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"编辑按键刷新测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result and test2_result and test3_result:
        print("\n🎉 所有测试通过！自动添加技能性能优化成功。")
        print("\n💡 优化效果:")
        print("1. 自动添加技能时UI立即完全重建，无延迟")
        print("2. 编辑按键后UI立即刷新显示新按键")
        print("3. 强制更新机制确保所有操作立即生效")
        print("4. 技能图标不再一个一个慢慢出现")
        print("5. 用户体验大幅提升，操作响应迅速")
    else:
        print("\n⚠️  部分测试失败，需要进一步优化。")
        
        if not test1_result:
            print("- 自动添加技能性能需要优化")
        if not test2_result:
            print("- UI更新机制需要完善")
        if not test3_result:
            print("- 编辑按键刷新功能需要修复")

if __name__ == "__main__":
    main()
