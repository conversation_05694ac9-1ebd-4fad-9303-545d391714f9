#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新的配置管理器 - 改进版本
支持层次化配置结构和更好的组织方式
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class ProfileMetadata:
    """配置文件元数据"""
    name: str
    description: str
    class_name: str = ""
    spec_name: str = ""
    version: str = "1.0.0"
    created_at: str = ""
    updated_at: str = ""
    author: str = ""
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = self.created_at

@dataclass
class SkillConfig:
    """技能配置"""
    name: str
    category: str
    hotkey: str
    priority: int = 1
    cooldown: float = 0.5
    template: str = ""
    threshold: float = 0.9
    enabled: bool = True
    description: str = ""
    combo: List[str] = None
    conditions: Dict[str, Any] = None
    stats: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.combo is None:
            self.combo = []
        if self.conditions is None:
            self.conditions = {}
        if self.stats is None:
            self.stats = {
                "usage_count": 0,
                "avg_similarity": 0.0,
                "last_used": ""
            }

@dataclass
class MonitorConfig:
    """监控配置"""
    region: Dict[str, int]
    enabled: bool = True
    auto_detect: bool = True

@dataclass
class DetectionSettings:
    """检测设置"""
    scan_interval: float = 0.33
    threshold: float = 0.9
    auto_add_skills: bool = True
    detection_method: str = "hash_comparison"

@dataclass
class InputSettings:
    """输入设置"""
    key_press_delay: float = 0.19
    monitor_hotkey: str = "`"
    enable_combos: bool = True
    smart_casting: bool = True

class ConfigManagerV2:
    """新版配置管理器"""
    
    def __init__(self, base_dir: str = "configs"):
        self.base_dir = Path(base_dir)
        self.profiles_dir = self.base_dir / "profiles"
        self.global_dir = self.base_dir / "global"
        self.schemas_dir = self.base_dir / "schemas"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载全局默认设置
        self.global_settings = self._load_global_settings()
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.profiles_dir,
            self.global_dir,
            self.schemas_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _load_global_settings(self) -> Dict[str, Any]:
        """加载全局默认设置"""
        global_settings_path = self.global_dir / "settings.json"
        
        default_settings = {
            "defaults": {
                "detection": {
                    "scan_interval": 0.5,
                    "threshold": 0.85,
                    "auto_add_skills": False
                },
                "input": {
                    "key_press_delay": 0.2,
                    "monitor_hotkey": "`"
                }
            },
            "ui": {
                "language": "zh-CN",
                "theme": "auto",
                "startup_profile": "last_used"
            }
        }
        
        if global_settings_path.exists():
            try:
                with open(global_settings_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载全局设置失败: {e}")
                return default_settings
        else:
            # 创建默认全局设置文件
            self._save_json(global_settings_path, default_settings)
            return default_settings
    
    def _save_json(self, path: Path, data: Any):
        """保存JSON文件"""
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _load_json(self, path: Path) -> Optional[Dict[str, Any]]:
        """加载JSON文件"""
        if not path.exists():
            return None
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载文件失败 {path}: {e}")
            return None
    
    def create_profile(self, profile_id: str, metadata: ProfileMetadata) -> bool:
        """创建新的配置文件"""
        try:
            profile_dir = self.profiles_dir / profile_id
            profile_dir.mkdir(exist_ok=True)
            
            # 创建模板目录
            templates_dir = profile_dir / "templates"
            templates_dir.mkdir(exist_ok=True)
            
            # 保存主配置文件
            profile_config = {
                "metadata": asdict(metadata),
                "monitor": {
                    "region": {"x": 0, "y": 0, "width": 50, "height": 50},
                    "enabled": True,
                    "auto_detect": True
                },
                "ui": {
                    "theme": "dark",
                    "layout": "grid",
                    "icon_size": 32,
                    "show_stats": True
                }
            }
            
            self._save_json(profile_dir / "profile.json", profile_config)
            
            # 创建空的技能配置
            skills_config = {
                "skills": {},
                "categories": {
                    "基础技能": {
                        "color": "#4CAF50",
                        "description": "常用的基础攻击技能"
                    },
                    "爆发技能": {
                        "color": "#FF5722",
                        "description": "高伤害爆发技能"
                    },
                    "辅助技能": {
                        "color": "#2196F3",
                        "description": "辅助和增益技能"
                    }
                }
            }
            
            self._save_json(profile_dir / "skills.json", skills_config)
            
            # 创建默认设置
            settings_config = {
                "detection": asdict(DetectionSettings()),
                "input": asdict(InputSettings()),
                "performance": {
                    "max_fps": 30,
                    "cache_templates": True,
                    "parallel_detection": False,
                    "memory_limit": "512MB"
                },
                "notifications": {
                    "sound_enabled": True,
                    "visual_feedback": True,
                    "log_level": "info"
                }
            }
            
            self._save_json(profile_dir / "settings.json", settings_config)
            
            print(f"成功创建配置文件: {profile_id}")
            return True
            
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            return False
    
    def load_profile(self, profile_id: str) -> Optional[Dict[str, Any]]:
        """加载完整的配置文件"""
        profile_dir = self.profiles_dir / profile_id
        
        if not profile_dir.exists():
            print(f"配置文件不存在: {profile_id}")
            return None
        
        try:
            # 加载各个配置文件
            profile_config = self._load_json(profile_dir / "profile.json")
            skills_config = self._load_json(profile_dir / "skills.json")
            settings_config = self._load_json(profile_dir / "settings.json")
            
            if not all([profile_config, skills_config, settings_config]):
                print(f"配置文件不完整: {profile_id}")
                return None
            
            # 合并配置
            full_config = {
                "profile_id": profile_id,
                "metadata": profile_config["metadata"],
                "monitor": profile_config["monitor"],
                "ui": profile_config["ui"],
                "skills": skills_config["skills"],
                "categories": skills_config["categories"],
                "settings": settings_config
            }
            
            print(f"成功加载配置文件: {profile_id}")
            return full_config
            
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return None
    
    def save_profile(self, profile_id: str, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        profile_dir = self.profiles_dir / profile_id
        
        try:
            # 更新时间戳
            config["metadata"]["updated_at"] = datetime.now().isoformat()
            
            # 分别保存各个配置文件
            profile_config = {
                "metadata": config["metadata"],
                "monitor": config["monitor"],
                "ui": config["ui"]
            }
            
            skills_config = {
                "skills": config["skills"],
                "categories": config["categories"]
            }
            
            settings_config = config["settings"]
            
            self._save_json(profile_dir / "profile.json", profile_config)
            self._save_json(profile_dir / "skills.json", skills_config)
            self._save_json(profile_dir / "settings.json", settings_config)
            
            print(f"成功保存配置文件: {profile_id}")
            return True
            
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def list_profiles(self) -> List[Dict[str, Any]]:
        """列出所有配置文件"""
        profiles = []
        
        for profile_dir in self.profiles_dir.iterdir():
            if profile_dir.is_dir():
                profile_config = self._load_json(profile_dir / "profile.json")
                if profile_config and "metadata" in profile_config:
                    metadata = profile_config["metadata"]
                    profiles.append({
                        "id": profile_dir.name,
                        "name": metadata.get("name", profile_dir.name),
                        "description": metadata.get("description", ""),
                        "class": metadata.get("class_name", ""),
                        "spec": metadata.get("spec_name", ""),
                        "updated_at": metadata.get("updated_at", "")
                    })
        
        # 按更新时间排序
        profiles.sort(key=lambda x: x["updated_at"], reverse=True)
        return profiles
    
    def delete_profile(self, profile_id: str) -> bool:
        """删除配置文件"""
        profile_dir = self.profiles_dir / profile_id
        
        if not profile_dir.exists():
            print(f"配置文件不存在: {profile_id}")
            return False
        
        try:
            import shutil
            shutil.rmtree(profile_dir)
            print(f"成功删除配置文件: {profile_id}")
            return True
        except Exception as e:
            print(f"删除配置文件失败: {e}")
            return False
    
    def migrate_old_config(self, old_config_path: str, new_profile_id: str) -> bool:
        """迁移旧配置到新格式"""
        try:
            # 加载旧配置
            with open(old_config_path, 'r', encoding='utf-8') as f:
                old_config = json.load(f)
            
            # 创建元数据
            metadata = ProfileMetadata(
                name=f"迁移的配置 - {new_profile_id}",
                description="从旧格式迁移的配置",
                version="1.0.0"
            )
            
            # 创建新配置文件
            if not self.create_profile(new_profile_id, metadata):
                return False
            
            # 转换配置数据
            new_config = self.load_profile(new_profile_id)
            
            # 迁移监控区域
            if "monitor_region" in old_config:
                region = old_config["monitor_region"]
                new_config["monitor"]["region"] = {
                    "x": region[0],
                    "y": region[1],
                    "width": region[2],
                    "height": region[3]
                }
            
            # 迁移设置
            if "settings" in old_config:
                old_settings = old_config["settings"]
                new_config["settings"]["detection"].update({
                    "scan_interval": old_settings.get("scan_interval", 0.33),
                    "threshold": old_settings.get("threshold", 0.9),
                    "auto_add_skills": old_settings.get("auto_add_skills", True)
                })
                new_config["settings"]["input"].update({
                    "key_press_delay": old_settings.get("key_press_delay", 0.19),
                    "monitor_hotkey": old_settings.get("monitor_hotkey", "`")
                })
            
            # 迁移技能绑定
            if "icon_bindings" in old_config:
                for skill_id, skill_data in old_config["icon_bindings"].items():
                    skill_config = SkillConfig(
                        name=skill_data.get("text", skill_id),
                        category="基础技能",
                        hotkey=skill_data["hotkey"],
                        template=f"{skill_id}.png"
                    )
                    new_config["skills"][skill_id] = asdict(skill_config)
            
            # 保存新配置
            return self.save_profile(new_profile_id, new_config)
            
        except Exception as e:
            print(f"迁移配置失败: {e}")
            return False

# 使用示例
if __name__ == "__main__":
    # 创建配置管理器
    config_manager = ConfigManagerV2()
    
    # 创建示例配置
    metadata = ProfileMetadata(
        name="冰霜法师配置",
        description="适用于团本的冰霜法师技能配置",
        class_name="法师",
        spec_name="冰霜",
        author="测试用户",
        tags=["团本", "高伤害", "AOE"]
    )
    
    # 创建配置文件
    if config_manager.create_profile("frost_mage_raid", metadata):
        print("配置文件创建成功")
    
    # 列出所有配置
    profiles = config_manager.list_profiles()
    print(f"找到 {len(profiles)} 个配置文件:")
    for profile in profiles:
        print(f"  - {profile['name']} ({profile['id']})")
